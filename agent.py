import os
import asyncio
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)
from config import model_manager, ModelType
from utils.file_oper import pickle_read, pickle_write


class CodebaseAgent:
    """代码库检索Agent"""

    def __init__(self):
        self.__data: Dict[str, Any] = {}
        self.pkl_path = os.path.join(os.path.dirname(__file__), "agent.pkl")

    def _grep(self, cmd: str) -> str:
        """执行grep命令"""
        try:
            # 这里可以实现grep功能
            return f"grep结果: {cmd}"
        except Exception as e:
            return f"grep执行失败: {str(e)}"

    def _glob(self, cmd: str) -> str:
        """执行glob命令"""
        try:
            # 这里可以实现glob功能
            return f"glob结果: {cmd}"
        except Exception as e:
            return f"glob执行失败: {str(e)}"

    def _system_call(self, cmd: str) -> str:
        """执行系统命令"""
        try:
            # 这里可以实现系统调用功能
            return f"系统调用结果: {cmd}"
        except Exception as e:
            return f"系统调用失败: {str(e)}"

    async def _llm_call(self, model_type: str, prompt: str) -> str:
        """调用LLM模型"""
        try:
            result = await model_manager.chat_completion(prompt, ModelType.FLASH)
            return result
        except Exception as e:
            return f"LLM调用失败: {str(e)}"

    def startup(self, **kwargs):
        """启动Agent"""
        repo_path = kwargs.get("repo_path")
        if not repo_path:
            raise ValueError("repo_path参数是必需的")

        # 加载现有数据
        if os.path.exists(self.pkl_path):
            self.__data = pickle_read(self.pkl_path)

        # 检查是否需要增量更新或全量更新
        if repo_path in self.__data:
            logger.info(f"检测到增量更新: {repo_path}")
            # 增量更新逻辑
            self._incremental_update(repo_path)
        else:
            logger.info(f"执行全量更新: {repo_path}")
            # 全量更新逻辑
            self._full_update(repo_path)

        # 保存数据
        pickle_write(self.pkl_path, self.__data)
        logger.info("Agent启动完成")

    def _incremental_update(self, repo_path: str):
        """增量更新逻辑"""
        # 这里实现增量更新逻辑
        logger.info(f"执行增量更新: {repo_path}")
        # TODO: 实现增量更新

    def _full_update(self, repo_path: str):
        """全量更新逻辑"""
        # 这里实现全量更新逻辑
        logger.info(f"执行全量更新: {repo_path}")
        # TODO: 实现全量更新

    async def query(self, query_string: str, use_flash: bool = False) -> Dict[str, Any]:
        """处理查询"""
        try:
            # 选择模型类型
            model_type = ModelType.FLASH

            # 调用LLM
            response = await model_manager.chat_completion(query_string, model_type)

            # 生成嵌入向量（用于后续检索）
            embedding = await model_manager.embedding(query_string)

            return {
                "response": response,
                "embedding": embedding,
                "model_used": model_type.value,
                "query": query_string,
            }
        except Exception as e:
            return {"error": f"查询处理失败: {str(e)}", "query": query_string}

    def sync_query(self, query_string: str, use_flash: bool = False) -> Dict[str, Any]:
        """同步查询处理"""
        return asyncio.run(self.query(query_string, use_flash))

    def refresh(self):
        """刷新Agent状态"""
        try:
            # 重新加载数据
            if os.path.exists(self.pkl_path):
                self.__data = pickle_read(self.pkl_path)
            logger.info("Agent状态已刷新")
        except Exception as e:
            logger.error(f"刷新失败: {str(e)}")

    def get_status(self) -> Dict[str, Any]:
        """获取Agent状态"""
        return {
            "data_loaded": len(self.__data) > 0,
            "data_count": len(self.__data),
            "available_models": model_manager.list_models(),
            "pkl_path": self.pkl_path,
        }


# 全局Agent实例
agent = CodebaseAgent()


# 向后兼容的函数接口
def startup(**kwargs):
    """向后兼容的启动函数"""
    agent.startup(**kwargs)


def query(query_string: str, use_flash: bool = False) -> Dict[str, Any]:
    """向后兼容的查询函数"""
    return agent.sync_query(query_string, use_flash)


def refresh():
    """向后兼容的刷新函数"""
    agent.refresh()


# 异步版本的查询函数
async def async_query(query_string: str, use_flash: bool = False) -> Dict[str, Any]:
    """异步查询函数"""
    return await agent.query(query_string, use_flash)
