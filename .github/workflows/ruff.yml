name: Ruff

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  ruff:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install ruff
      run: pip install ruff
    
    - name: Run ruff linter
      run: ruff check .
    
    - name: Run ruff formatter
      run: ruff format --check .