"""
嵌入计算器
计算代码分片的嵌入向量
"""

import asyncio
from typing import List
from utils.interfaces import IEmbeddingCalculator
from utils.models import CodeChunk
from utils.helpers import truncate_text
from config import EMBEDDING_CONFIG, model_manager, ModelType
import logging

logger = logging.getLogger(__name__)


class EmbeddingCalculator(IEmbeddingCalculator):
    """嵌入计算器实现"""

    def __init__(self):
        self.batch_size = EMBEDDING_CONFIG["batch_size"]
        self.max_text_length = EMBEDDING_CONFIG["max_text_length"]
        self.embedding_dimension = EMBEDDING_CONFIG["embedding_dimension"]

    async def calculate_embedding(self, text: str) -> List[float]:
        """计算文本的嵌入向量"""
        max_retries = 5
        once_max_len = self.max_text_length
        for attempt in range(max_retries):
            try:
                # 截断过长的文本
                truncated_text = truncate_text(
                    text, min(once_max_len, self.max_text_length)
                )

                # 调用嵌入模型
                embedding = await model_manager.embedding(
                    truncated_text, model_type=ModelType.EMBEDDING
                )

                return embedding

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"计算嵌入向量失败 (尝试 {attempt + 1}/{max_retries}): {e}"
                    )
                    logger.warning(f"truncated_text: {truncated_text[:100]}")
                    try:
                        if "input must have less than" in str(e):
                            once_max_len = once_max_len // 2
                    except Exception as e:
                        logger.error(f"尝试解析报错识别失败: {e}")
                        pass
                    # 等待一段时间后重试，使用指数退避
                    await asyncio.sleep((2**attempt) * 0.1)
                else:
                    logger.error(f"计算嵌入向量最终失败 (已重试 {max_retries} 次): {e}")
                    # 返回零向量作为降级
                    return [0.0] * self.embedding_dimension

    async def batch_calculate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """批量计算嵌入向量"""
        embeddings = []
        total_texts = len(texts)

        # 分批处理
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i : i + self.batch_size]

            # 显示进度
            progress = min(i + self.batch_size, total_texts)
            logger.debug(
                f"  嵌入计算进度: {progress}/{total_texts} ({progress / total_texts * 100:.1f}%)"
            )

            # 并发计算嵌入
            batch_tasks = [self.calculate_embedding(text) for text in batch]
            batch_embeddings = await asyncio.gather(
                *batch_tasks, return_exceptions=True
            )

            # 处理异常结果
            for j, embedding in enumerate(batch_embeddings):
                if isinstance(embedding, Exception):
                    logger.warning(f"批量计算嵌入异常 {i + j}: {embedding}")
                    embedding = [0.0] * self.embedding_dimension

                embeddings.append(embedding)

            # 避免API调用过于频繁
            if i + self.batch_size < len(texts):
                await asyncio.sleep(0.1)  # 减少等待时间

        return embeddings

    def prepare_text_for_embedding(self, chunk: CodeChunk) -> str:
        """为嵌入计算准备文本"""
        # 组合多种信息来构建嵌入文本
        text_parts = []

        # 1. 文件路径信息
        text_parts.append(f"文件: {chunk.relative_path}")

        # 2. 符号信息
        if chunk.symbol_name:
            text_parts.append(f"符号: {chunk.symbol_name}")

        # 3. 类型信息
        text_parts.append(f"类型: {chunk.chunk_type}")

        # 4. 洞察信息（如果有）
        if chunk.insight:
            text_parts.append(f"功能: {chunk.insight}")

        # # 5. 关键词信息（如果有）
        # if chunk.keywords:
        #     keywords_text = ', '.join(chunk.keywords[:10])  # 限制关键词数量
        #     text_parts.append(f"关键词: {keywords_text}")

        # 6. 代码内容
        text_parts.append(f"内容: {chunk.content}")

        # 组合所有部分
        combined_text = "\n".join(text_parts)

        # 截断到最大长度
        return truncate_text(combined_text, self.max_text_length)

    async def calculate_chunk_embedding(self, chunk: CodeChunk) -> List[float]:
        """计算代码分片的嵌入向量"""
        text = self.prepare_text_for_embedding(chunk)
        return await self.calculate_embedding(text)

    async def update_chunk_embeddings(self, chunks: List[CodeChunk]) -> List[CodeChunk]:
        """更新代码分片的嵌入向量"""
        logger.debug(f"开始计算{len(chunks)}个代码分片的嵌入向量...")

        # 使用信号量控制并发数
        from config import EMBEDDING_CONFIG

        max_concurrent = EMBEDDING_CONFIG.get("max_concurrent_requests", 50)
        semaphore = asyncio.Semaphore(max_concurrent)

        async def calculate_with_semaphore(chunk):
            async with semaphore:
                text = self.prepare_text_for_embedding(chunk)
                return await self.calculate_embedding(text)

        # 创建所有任务
        tasks = [calculate_with_semaphore(chunk) for chunk in chunks]

        # 批量执行，显示进度
        batch_size = 100  # 进度显示的批次大小
        embeddings = []

        for i in range(0, len(tasks), batch_size):
            batch_tasks = tasks[i : i + batch_size]
            progress = min(i + batch_size, len(tasks))
            logger.debug(
                f"  嵌入计算进度: {progress}/{len(tasks)} ({progress / len(tasks) * 100:.1f}%)"
            )

            batch_embeddings = await asyncio.gather(
                *batch_tasks, return_exceptions=True
            )

            # 处理异常结果
            for j, embedding in enumerate(batch_embeddings):
                if isinstance(embedding, Exception):
                    chunk_idx = i + j
                    logger.warning(f"计算嵌入异常 {chunks[chunk_idx].id}: {embedding}")
                    embedding = [0.0] * self.embedding_dimension
                embeddings.append(embedding)

        # 更新分片的嵌入信息
        for chunk, embedding in zip(chunks, embeddings):
            chunk.embedding = embedding

        logger.debug(f"完成嵌入计算，成功处理{len(chunks)}个分片")
        return chunks

    def get_embedding_stats(self, chunks: List[CodeChunk]) -> dict:
        """获取嵌入向量统计信息"""
        embeddings_with_data = [
            chunk for chunk in chunks if chunk.embedding and any(chunk.embedding)
        ]
        zero_embeddings = len(chunks) - len(embeddings_with_data)

        stats = {
            "total_chunks": len(chunks),
            "embeddings_with_data": len(embeddings_with_data),
            "zero_embeddings": zero_embeddings,
            "embedding_dimension": self.embedding_dimension,
            "success_rate": len(embeddings_with_data) / len(chunks) if chunks else 0,
        }

        if embeddings_with_data:
            # 计算向量的平均长度（L2范数）
            vector_norms = []
            for chunk in embeddings_with_data:
                if chunk.embedding:
                    norm = sum(x * x for x in chunk.embedding) ** 0.5
                    vector_norms.append(norm)

            if vector_norms:
                stats["avg_vector_norm"] = sum(vector_norms) / len(vector_norms)
                stats["min_vector_norm"] = min(vector_norms)
                stats["max_vector_norm"] = max(vector_norms)

        return stats
