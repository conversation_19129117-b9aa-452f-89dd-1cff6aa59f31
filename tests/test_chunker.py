"""
代码分片器测试
"""

import unittest
from utils.models import FileInfo, CodeChunk
from codebase_processor.chunker import CodeChunker


class TestCodeChunker(unittest.TestCase):
    """代码分片器测试类"""

    def setUp(self):
        """测试初始化"""
        self.chunker = CodeChunker()

        # 创建测试文件信息
        self.python_file = FileInfo(
            file_path="/test/example.py",
            relative_path="example.py",
            language="python",
            content="""def hello_world():
    print("Hello, World!")
    return "success"

class TestClass:
    def __init__(self):
        self.name = "test"
    
    def get_name(self):
        return self.name
""",
            size=200,
            crc32="12345678",
        )

        self.markdown_file = FileInfo(
            file_path="/test/README.md",
            relative_path="README.md",
            language="markdown",
            content="""# 项目标题

这是项目介绍。

## 安装说明

运行以下命令安装：

```bash
pip install package
```

## 使用方法

简单的使用示例：

```python
import package
package.run()
```
""",
            size=150,
            crc32="87654321",
        )

    def test_chunk_by_symbols_simple_parse(self):
        """测试基于符号的分片（简单解析）"""
        # 模拟简单解析结果
        parse_result = {
            "type": "simple_parse",
            "symbols": [
                {"type": "function_definition", "name": "hello_world", "start_line": 1},
                {"type": "class_definition", "name": "TestClass", "start_line": 5},
            ],
            "lines": self.python_file.content.split("\n"),
        }

        chunks = self.chunker.chunk_by_symbols(parse_result, self.python_file)

        # 验证结果
        self.assertGreater(len(chunks), 0)
        self.assertIsInstance(chunks[0], CodeChunk)

        # 验证第一个分片（函数）
        func_chunk = chunks[0]
        self.assertEqual(func_chunk.symbol_name, "hello_world")
        self.assertEqual(func_chunk.chunk_type, "function")
        self.assertEqual(func_chunk.language, "python")
        self.assertIn("def hello_world", func_chunk.content)

    def test_chunk_markdown(self):
        """测试Markdown分片"""
        chunks = self.chunker.chunk_markdown(self.markdown_file)

        # 验证结果
        self.assertGreater(len(chunks), 0)

        # 验证第一个分片（标题）
        title_chunk = chunks[0]
        self.assertEqual(title_chunk.chunk_type, "markdown_section")
        self.assertEqual(title_chunk.language, "markdown")
        self.assertIn("项目标题", title_chunk.content)

    def test_chunk_by_lines_fallback(self):
        """测试按行分片（降级方案）"""
        chunks = self.chunker._chunk_by_lines(self.python_file)

        # 验证结果
        self.assertGreater(len(chunks), 0)

        # 验证分片属性
        chunk = chunks[0]
        self.assertEqual(chunk.chunk_type, "other")
        self.assertEqual(chunk.language, "python")
        self.assertGreater(len(chunk.content), 0)

    def test_create_chunk(self):
        """测试创建分片"""
        lines = ["def test():", "    return True"]
        chunk = self.chunker._create_chunk(
            lines, 1, 2, "test", "function_definition", self.python_file
        )

        # 验证分片属性
        self.assertIsNotNone(chunk)
        self.assertEqual(chunk.symbol_name, "test")
        self.assertEqual(chunk.chunk_type, "function")
        self.assertEqual(chunk.start_line, 1)
        self.assertEqual(chunk.end_line, 2)
        self.assertIn("def test", chunk.content)

    def test_split_large_symbol(self):
        """测试分割大符号"""
        # 创建一个很长的符号内容
        long_lines = ["def long_function():"] + [f"    line_{i}" for i in range(150)]

        chunks = self.chunker._split_large_symbol(
            long_lines, 1, "long_function", "function_definition", self.python_file
        )

        # 验证分割结果
        self.assertGreater(len(chunks), 1)  # 应该被分割成多个分片

        # 验证每个分片的大小
        for chunk in chunks:
            self.assertLessEqual(
                len(chunk.content.split("\n")), self.chunker.chunk_max_lines
            )


if __name__ == "__main__":
    unittest.main()
