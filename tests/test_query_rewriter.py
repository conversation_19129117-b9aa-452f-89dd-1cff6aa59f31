"""
查询重写器测试
"""

import unittest
from unittest.mock import AsyncMock, patch
from retrieval_service.query_rewriter import QueryRewriter


class TestQueryRewriter(unittest.TestCase):
    """查询重写器测试类"""

    def setUp(self):
        """测试初始化"""
        self.rewriter = QueryRewriter()

    def test_extract_keywords_from_query(self):
        """测试从查询中提取关键词"""
        # 测试中英文混合查询
        query = "如何实现用户认证 authentication function"
        keywords = self.rewriter._extract_keywords_from_query(query)

        # 验证关键词提取
        self.assertIsInstance(keywords, list)
        self.assertIn("authentication", keywords)
        self.assertIn("function", keywords)
        self.assertIn("用户认证", keywords)

        # 验证停用词被过滤
        self.assertNotIn("如何", keywords)
        self.assertNotIn("实现", keywords)

    def test_rule_based_rewrite(self):
        """测试基于规则的查询重写"""
        # 测试函数查询
        query = "用户登录函数 login function"
        patterns = self.rewriter._rule_based_rewrite(query)

        # 验证生成的模式
        self.assertIsInstance(patterns, list)
        self.assertGreater(len(patterns), 0)

        # 验证包含函数相关的模式
        patterns_str = " ".join(patterns)
        self.assertTrue(
            any("def" in pattern or "function" in pattern for pattern in patterns)
        )

    def test_rule_based_rewrite_class(self):
        """测试类相关的查询重写"""
        query = "用户管理类 UserManager class"
        patterns = self.rewriter._rule_based_rewrite(query)

        # 验证包含类相关的模式
        patterns_str = " ".join(patterns)
        self.assertTrue(any("class" in pattern for pattern in patterns))

    def test_rewrite_to_embedding_query(self):
        """测试嵌入检索查询重写"""
        query = "如何实现用户认证功能？"
        embedding_query = self.rewriter.rewrite_to_embedding_query(query)

        # 验证查询清理
        self.assertIsInstance(embedding_query, str)
        self.assertNotIn("如何", embedding_query)
        self.assertIn("用户认证", embedding_query)
        self.assertIn("功能", embedding_query)

    def test_generate_fallback_patterns(self):
        """测试生成降级搜索模式"""
        query = "getUserData function implementation"
        patterns = self.rewriter.generate_fallback_patterns(query)

        # 验证降级模式
        self.assertIsInstance(patterns, list)
        self.assertGreater(len(patterns), 0)
        self.assertIn("getUserData", patterns)
        self.assertIn("function", patterns)

    @patch("retrieval_service.query_rewriter.model_manager")
    async def test_rewrite_to_ripgrep_with_llm(self, mock_model_manager):
        """测试使用LLM的ripgrep重写"""
        # 模拟LLM响应
        mock_model_manager.chat_completion = AsyncMock(
            return_value="""
def.*login
class.*User
login.*function
        """
        )

        repo_insight = "这是一个用户管理系统，包含登录和认证功能。"
        query = "用户登录功能"

        patterns = await self.rewriter.rewrite_to_ripgrep(query, repo_insight)

        # 验证LLM重写结果
        self.assertIsInstance(patterns, list)
        self.assertGreater(len(patterns), 0)

        # 验证调用了LLM
        mock_model_manager.chat_completion.assert_called_once()

    @patch("retrieval_service.query_rewriter.model_manager")
    async def test_rewrite_to_ripgrep_fallback(self, mock_model_manager):
        """测试LLM失败时的降级重写"""
        # 模拟LLM调用失败
        mock_model_manager.chat_completion = AsyncMock(
            side_effect=Exception("LLM调用失败")
        )

        repo_insight = "测试仓库"
        query = "用户登录功能"

        patterns = await self.rewriter.rewrite_to_ripgrep(query, repo_insight)

        # 验证降级到规则重写
        self.assertIsInstance(patterns, list)
        self.assertGreater(len(patterns), 0)

    def test_parse_ripgrep_response(self):
        """测试解析ripgrep响应"""
        response = """
1. def.*login.*function
2. class.*User.*Manager
3. login.*authentication
        """

        patterns = self.rewriter._parse_ripgrep_response(response)

        # 验证解析结果
        self.assertEqual(len(patterns), 3)
        self.assertIn("def.*login.*function", patterns)
        self.assertIn("class.*User.*Manager", patterns)
        self.assertIn("login.*authentication", patterns)

    def test_concept_mappings(self):
        """测试概念映射"""
        # 验证概念映射字典存在
        self.assertIsInstance(self.rewriter.concept_mappings, dict)
        self.assertIn("函数", self.rewriter.concept_mappings)
        self.assertIn("类", self.rewriter.concept_mappings)

        # 验证映射内容
        func_mappings = self.rewriter.concept_mappings["函数"]
        self.assertIn("function", func_mappings)
        self.assertIn("def", func_mappings)


if __name__ == "__main__":
    unittest.main()
