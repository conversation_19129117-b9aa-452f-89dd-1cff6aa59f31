"""
代码洞察生成器
使用LLM为代码分片生成功能描述和洞察
"""

import asyncio
from typing import List
from utils.interfaces import ICodeInsightGenerator
from utils.models import CodeChunk
from utils.helpers import truncate_text
from config import INSIGHT_CONFIG, model_manager, ModelType
import logging

logger = logging.getLogger(__name__)


class CodeInsightGenerator(ICodeInsightGenerator):
    """代码洞察生成器实现"""

    def __init__(self):
        self.batch_size = INSIGHT_CONFIG["batch_size"]
        self.max_insight_length = INSIGHT_CONFIG["max_insight_length"]
        self.prompt_template = INSIGHT_CONFIG["insight_prompt_template"]

    async def generate_insight(self, chunk: CodeChunk) -> str:
        """为代码分片生成洞察描述"""
        try:
            # 构建提示词
            prompt = self._build_prompt(chunk)

            # 调用LLM生成洞察
            insight = await model_manager.chat_completion(
                prompt,
                model_type=ModelType.FLASH,  # 使用快速模型
            )

            # 清理和截断结果
            cleaned_insight = self._clean_insight(insight)
            return truncate_text(cleaned_insight, self.max_insight_length)

        except Exception as e:
            logger.error(f"生成洞察失败 {chunk.id}: {e}")
            # 返回基础描述作为降级
            return self._generate_fallback_insight(chunk)

    async def batch_generate_insights(self, chunks: List[CodeChunk]) -> List[str]:
        """批量生成洞察描述"""
        insights = []
        total_chunks = len(chunks)

        # 分批处理
        for i in range(0, len(chunks), self.batch_size):
            batch = chunks[i : i + self.batch_size]

            # 显示进度
            progress = min(i + self.batch_size, total_chunks)
            logger.debug(
                f"  洞察生成进度: {progress}/{total_chunks} ({progress / total_chunks * 100:.1f}%)"
            )

            # 并发生成洞察
            batch_tasks = [self.generate_insight(chunk) for chunk in batch]
            batch_insights = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # 处理异常结果
            for j, insight in enumerate(batch_insights):
                if isinstance(insight, Exception):
                    logger.warning(f"批量生成洞察异常 {batch[j].id}: {insight}")
                    insight = self._generate_fallback_insight(batch[j])

                insights.append(insight)

            # 避免API调用过于频繁
            if i + self.batch_size < len(chunks):
                await asyncio.sleep(0.1)  # 减少等待时间

        return insights

    def _build_prompt(self, chunk: CodeChunk) -> str:
        """构建LLM提示词"""
        # 截断过长的代码内容
        code_content = truncate_text(chunk.content, 30_000)

        return self.prompt_template.format(
            file_path=chunk.relative_path,
            chunk_type=chunk.chunk_type,
            symbol_name=chunk.symbol_name,
            language=chunk.language,
            code_content=code_content,
        )

    def _clean_insight(self, insight: str) -> str:
        """清理LLM生成的洞察文本"""
        if not insight:
            return ""

        # 移除多余的空白字符
        cleaned = " ".join(insight.split())

        # 移除可能的引号包围
        if cleaned.startswith('"') and cleaned.endswith('"'):
            cleaned = cleaned[1:-1]
        elif cleaned.startswith("'") and cleaned.endswith("'"):
            cleaned = cleaned[1:-1]

        # 确保以句号结尾
        if cleaned and not cleaned.endswith((".", "。", "!", "！", "?", "？")):
            cleaned += "。"

        return cleaned

    def _generate_fallback_insight(self, chunk: CodeChunk) -> str:
        """生成降级洞察描述"""
        if chunk.chunk_type == "function":
            return f"这是一个名为{chunk.symbol_name}的函数，位于{chunk.relative_path}文件中。"
        elif chunk.chunk_type == "class":
            return f"这是一个名为{chunk.symbol_name}的类定义，位于{chunk.relative_path}文件中。"
        elif chunk.chunk_type == "method":
            return f"这是一个名为{chunk.symbol_name}的方法，位于{chunk.relative_path}文件中。"
        elif chunk.chunk_type == "markdown_section":
            return f"这是{chunk.relative_path}文档中的{chunk.symbol_name}章节。"
        else:
            return f"这是{chunk.relative_path}文件中的一段{chunk.language}代码。"

    def generate_repo_insight(self, chunks: List[CodeChunk]) -> str:
        """生成仓库整体洞察"""
        if not chunks:
            return "这是一个空的代码仓库。"

        # 统计信息
        total_chunks = len(chunks)
        languages = set(chunk.language for chunk in chunks)
        files = set(chunk.relative_path for chunk in chunks)

        # 按类型统计
        type_counts = {}
        for chunk in chunks:
            chunk_type = chunk.chunk_type
            type_counts[chunk_type] = type_counts.get(chunk_type, 0) + 1

        # 构建洞察描述
        insight_parts = [
            f"这个代码仓库包含{len(files)}个文件，共{total_chunks}个代码片段。",
            f"支持的编程语言包括：{', '.join(sorted(languages))}。",
        ]

        # 添加类型统计
        if type_counts:
            type_desc = []
            for chunk_type, count in sorted(type_counts.items()):
                if chunk_type == "function":
                    type_desc.append(f"{count}个函数")
                elif chunk_type == "class":
                    type_desc.append(f"{count}个类")
                elif chunk_type == "method":
                    type_desc.append(f"{count}个方法")
                elif chunk_type == "markdown_section":
                    type_desc.append(f"{count}个文档章节")
                else:
                    type_desc.append(f"{count}个其他代码片段")

            if type_desc:
                insight_parts.append(f"包含{', '.join(type_desc)}。")

        # 添加主要文件信息
        main_files = [
            f
            for f in files
            if any(
                keyword in f.lower() for keyword in ["main", "index", "app", "server"]
            )
        ]
        if main_files:
            insight_parts.append(f"主要入口文件可能包括：{', '.join(main_files[:3])}。")

        return " ".join(insight_parts)

    async def update_chunk_insights(self, chunks: List[CodeChunk]) -> List[CodeChunk]:
        """更新代码分片的洞察信息"""
        logger.debug(f"开始生成{len(chunks)}个代码分片的洞察...")

        # 使用信号量控制并发数
        from config import INSIGHT_CONFIG

        max_concurrent = INSIGHT_CONFIG.get("max_concurrent_requests", 50)
        semaphore = asyncio.Semaphore(max_concurrent)

        async def generate_with_semaphore(chunk):
            async with semaphore:
                return await self.generate_insight(chunk)

        # 创建所有任务
        tasks = [generate_with_semaphore(chunk) for chunk in chunks]

        # 批量执行，显示进度
        batch_size = 50  # 进度显示的批次大小
        insights = []

        for i in range(0, len(tasks), batch_size):
            batch_tasks = tasks[i : i + batch_size]
            progress = min(i + batch_size, len(tasks))
            logger.debug(
                f"  洞察生成进度: {progress}/{len(tasks)} ({progress / len(tasks) * 100:.1f}%)"
            )

            batch_insights = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # 处理异常结果
            for j, insight in enumerate(batch_insights):
                if isinstance(insight, Exception):
                    chunk_idx = i + j
                    logger.warning(f"生成洞察异常 {chunks[chunk_idx].id}: {insight}")
                    insight = self._generate_fallback_insight(chunks[chunk_idx])
                insights.append(insight)

        # 更新分片的洞察信息
        for chunk, insight in zip(chunks, insights):
            chunk.insight = insight

        logger.debug(f"完成洞察生成，成功处理{len(chunks)}个分片")
        return chunks
