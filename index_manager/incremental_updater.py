"""
增量更新机制
检测文件变更并增量更新索引
"""

import os
import fnmatch
from typing import List, Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)
from pathlib import Path
from config import ignore_config
from utils.models import FileInfo, CodeChunk
from utils.helpers import calculate_file_crc32, get_relative_path
from index_manager.repo_index import RepoIndex
from codebase_processor.loader import CodebaseLoader
from codebase_processor.parser import TreeSitterParser
from codebase_processor.chunker import CodeChunker
from insight_generator.code_insight import CodeInsightGenerator
from insight_generator.keyword_extractor import KeywordExtractor
from embedding_processor.calculator import EmbeddingCalculator
from symbol_processor import SymbolExtractor
from symbol_processor.symbol_index import SymbolIndex


class IncrementalUpdater:
    """增量更新器"""

    def __init__(self, repo_index: RepoIndex, symbol_index: SymbolIndex | None = None):
        self.repo_index = repo_index
        self.symbol_index = symbol_index
        self.loader = CodebaseLoader()
        self.parser = TreeSitterParser()
        self.chunker = CodeChunker()
        self.insight_generator = CodeInsightGenerator()
        self.keyword_extractor = KeywordExtractor()
        self.embedding_calculator = EmbeddingCalculator()
        self.index_dir: Optional[str] = None
        self.symbol_extractor = SymbolExtractor()

    def set_index_dir(self, index_dir: str):
        """设置索引目录，用于增量保存"""
        self.index_dir = index_dir

    def _should_ignore_path(self, path: str, repo_path: str) -> bool:
        """检查路径是否应该被忽略"""
        # 获取相对路径
        try:
            relative_path = get_relative_path(path, repo_path)
        except:
            relative_path = os.path.relpath(path, repo_path)

        # 检查每个忽略模式
        for pattern in ignore_config.all_ignore_patterns:
            # 使用fnmatch进行模式匹配
            if fnmatch.fnmatch(relative_path, pattern) or fnmatch.fnmatch(
                os.path.basename(path), pattern
            ):
                return True

            # 检查路径的任何部分是否匹配模式
            path_parts = relative_path.split(os.sep)
            for part in path_parts:
                if fnmatch.fnmatch(part, pattern.replace("**/", "").replace("/**", "")):
                    return True

        return False

    def detect_file_changes(
        self, repo_path: str
    ) -> Tuple[List[str], List[str], List[str]]:
        """检测文件变更

        Returns:
            Tuple[added_files, modified_files, deleted_files]
        """
        # 获取当前文件列表（已经通过loader过滤了忽略的文件）
        current_files = self.loader.load_files(repo_path)
        current_file_map = {info.relative_path: info for info in current_files}

        # 获取索引中的文件校验和
        indexed_checksums = self.repo_index.file_checksums

        added_files = []
        modified_files = []
        deleted_files = []

        # 检查新增和修改的文件
        for relative_path, file_info in current_file_map.items():
            if relative_path not in indexed_checksums:
                # 新文件
                added_files.append(relative_path)
            elif indexed_checksums[relative_path] != file_info.crc32:
                # 修改的文件
                modified_files.append(relative_path)

        # 检查删除的文件
        for relative_path in indexed_checksums:
            if relative_path not in current_file_map:
                deleted_files.append(relative_path)

        return added_files, modified_files, deleted_files

    async def update_index(self, repo_path: str) -> Dict[str, int]:
        """增量更新索引

        Returns:
            更新统计信息
        """
        logger.debug("开始检测文件变更...")
        added_files, modified_files, deleted_files = self.detect_file_changes(repo_path)

        stats = {
            "added_files": len(added_files),
            "modified_files": len(modified_files),
            "deleted_files": len(deleted_files),
            "added_chunks": 0,
            "modified_chunks": 0,
            "deleted_chunks": 0,
        }

        logger.debug(
            f"检测到变更: 新增{len(added_files)}个文件, 修改{len(modified_files)}个文件, 删除{len(deleted_files)}个文件"
        )

        # 处理删除的文件
        if deleted_files:
            stats["deleted_chunks"] = await self._handle_deleted_files(deleted_files)

        # 处理修改的文件
        if modified_files:
            stats["modified_chunks"] = await self._handle_modified_files(
                repo_path, modified_files
            )

        # 处理新增的文件
        if added_files:
            stats["added_chunks"] = await self._handle_added_files(
                repo_path, added_files
            )

        # 如果有任何变更，重新生成仓库洞察并保存元数据
        if any(
            stats[key] > 0
            for key in ["added_chunks", "modified_chunks", "deleted_chunks"]
        ):
            await self._update_repo_insight()
            # 保存更新后的元数据
            if self.index_dir:
                self.repo_index.save_metadata(self.index_dir)

        logger.debug(
            f"增量更新完成: 新增{stats['added_chunks']}个分片, 修改{stats['modified_chunks']}个分片, 删除{stats['deleted_chunks']}个分片"
        )

        return stats

    async def _handle_deleted_files(self, deleted_files: List[str]) -> int:
        """处理删除的文件"""
        deleted_chunks_count = 0

        for file_path in deleted_files:
            # 获取该文件的所有分片
            chunks = self.repo_index.get_chunks_by_file(file_path)
            deleted_chunks_count += len(chunks)

            # 从索引中删除该文件的所有分片
            self.repo_index.remove_chunks_by_file(file_path)

            # 同步删除符号索引
            if self.symbol_index:
                self.symbol_index.remove_symbols_by_file(file_path)

            # 删除该文件的索引文件
            if self.index_dir:
                self.repo_index.remove_file_index(self.index_dir, file_path)

            logger.info(f"删除文件 {file_path} 的 {len(chunks)} 个分片")

        return deleted_chunks_count

    async def _handle_modified_files(
        self, repo_path: str, modified_files: List[str]
    ) -> int:
        """并行处理修改的文件"""
        if not modified_files:
            return 0

        logger.info(f"并行处理 {len(modified_files)} 个修改文件...")

        # 先删除所有旧分片
        old_chunks_count = 0
        for relative_path in modified_files:
            logger.info(f"处理修改文件: {relative_path}")
            old_chunks = self.repo_index.get_chunks_by_file(relative_path)
            old_chunks_count += len(old_chunks)
            self.repo_index.remove_chunks_by_file(relative_path)
            # 删除旧的符号
            if self.symbol_index:
                self.symbol_index.remove_symbols_by_file(relative_path)

        # 并行处理文件
        import asyncio
        from config import INDEX_CONFIG

        max_concurrent = INDEX_CONFIG.get("max_concurrent_files", 5)
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_modified_file(relative_path):
            async with semaphore:
                file_path = str(Path(repo_path) / relative_path)
                try:
                    new_chunks = await self._process_single_file(file_path, repo_path)

                    # 立即保存该文件的索引
                    if self.index_dir:
                        self.repo_index.save_file_index(self.index_dir, relative_path)

                    return relative_path, len(new_chunks), None
                except Exception as e:
                    return relative_path, 0, e

        # 创建并执行任务
        tasks = [
            process_modified_file(relative_path) for relative_path in modified_files
        ]
        results = await asyncio.gather(*tasks)

        # 处理结果
        modified_chunks_count = 0
        for relative_path, chunk_count, error in results:
            if error:
                logger.warning(f"处理修改文件失败 {relative_path}: {error}")
            else:
                modified_chunks_count += chunk_count
                logger.info(f"更新文件 {relative_path}: 新增 {chunk_count} 个分片")

        logger.info(
            f"修改文件处理完成: 删除 {old_chunks_count} 个分片, 新增 {modified_chunks_count} 个分片"
        )
        return modified_chunks_count

    async def _handle_added_files(self, repo_path: str, added_files: List[str]) -> int:
        """并行处理新增的文件"""
        if not added_files:
            return 0

        logger.info(f"并行处理 {len(added_files)} 个新增文件...")

        # 并行处理文件
        import asyncio
        from config import INDEX_CONFIG

        max_concurrent = INDEX_CONFIG.get("max_concurrent_files", 5)
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_added_file(relative_path):
            async with semaphore:
                file_path = str(Path(repo_path) / relative_path)
                try:
                    new_chunks = await self._process_single_file(file_path, repo_path)

                    # 立即保存该文件的索引
                    if self.index_dir:
                        self.repo_index.save_file_index(self.index_dir, relative_path)

                    return relative_path, len(new_chunks), None
                except Exception as e:
                    return relative_path, 0, e

        # 创建并执行任务
        tasks = [process_added_file(relative_path) for relative_path in added_files]
        results = await asyncio.gather(*tasks)

        # 处理结果
        added_chunks_count = 0
        for relative_path, chunk_count, error in results:
            if error:
                logger.warning(f"处理新增文件失败 {relative_path}: {error}")
            else:
                added_chunks_count += chunk_count
                logger.info(f"新增文件 {relative_path}: 创建 {chunk_count} 个分片")

        logger.info(f"新增文件处理完成: 创建 {added_chunks_count} 个分片")
        return added_chunks_count

    async def _process_single_file(
        self, file_path: str, repo_path: str
    ) -> List[CodeChunk]:
        """完整处理单个文件，返回生成的分片"""
        # 加载文件信息
        file_info = self._create_file_info(file_path, repo_path)
        if not file_info:
            return []

        try:
            # 1. 解析文件
            parse_tree = self.parser.parse_file(file_info)
            if not parse_tree:
                return []

            # 提取符号并更新符号索引
            if self.symbol_index and file_info.language != "markdown":
                try:
                    symbols = self.symbol_extractor.extract_symbols(
                        parse_tree, file_info
                    )
                    for sym in symbols:
                        self.symbol_index.add_symbol(sym)
                except Exception as e:
                    print(f"符号提取失败 {file_info.relative_path}: {e}")

            # 2. 分片
            if file_info.language == "markdown":
                chunks = self.chunker.chunk_markdown(file_info)
            else:
                chunks = self.chunker.chunk_by_symbols(parse_tree, file_info)

            if not chunks:
                return []

            # 3. 生成洞察
            chunks = await self.insight_generator.update_chunk_insights(chunks)

            # 4. 提取关键词
            chunks = self.keyword_extractor.update_chunk_keywords(chunks)

            # 5. 计算嵌入向量
            chunks = await self.embedding_calculator.update_chunk_embeddings(chunks)

            # 6. 添加到索引
            for chunk in chunks:
                self.repo_index.add_chunk(chunk)

            # 7. 更新文件校验和
            self.repo_index.update_file_checksum(
                file_info.relative_path, file_info.crc32
            )

            return chunks

        except Exception as e:
            logger.error(f"完整处理文件失败 {file_info.relative_path}: {e}")
            return []

    def _create_file_info(self, file_path: str, repo_path: str) -> FileInfo:
        """创建文件信息对象"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, "r", encoding="gbk") as f:
                    content = f.read()
            except UnicodeDecodeError:
                with open(file_path, "r", encoding="latin-1") as f:
                    content = f.read()

        from utils.helpers import get_file_language

        language = get_file_language(file_path)
        if not language:
            return None

        relative_path = get_relative_path(file_path, repo_path)
        file_size = len(content.encode("utf-8"))
        crc32 = calculate_file_crc32(file_path)

        return FileInfo(
            file_path=file_path,
            relative_path=relative_path,
            language=language,
            content=content,
            size=file_size,
            crc32=crc32,
        )

    async def _update_repo_insight(self):
        """更新仓库整体洞察"""
        try:
            all_chunks = self.repo_index.get_all_chunks()
            repo_insight = self.insight_generator.generate_repo_insight(all_chunks)
            self.repo_index.set_repo_insight(repo_insight)
            logger.info("仓库洞察已更新")
        except Exception as e:
            logger.error(f"更新仓库洞察失败: {e}")

    def is_incremental_update_needed(self, repo_path: str) -> bool:
        """检查是否需要增量更新"""
        added_files, modified_files, deleted_files = self.detect_file_changes(repo_path)
        return len(added_files) > 0 or len(modified_files) > 0 or len(deleted_files) > 0

    def get_change_summary(self, repo_path: str) -> Dict:
        """获取变更摘要"""
        added_files, modified_files, deleted_files = self.detect_file_changes(repo_path)

        return {
            "has_changes": len(added_files) > 0
            or len(modified_files) > 0
            or len(deleted_files) > 0,
            "added_files": added_files,
            "modified_files": modified_files,
            "deleted_files": deleted_files,
            "total_changes": len(added_files)
            + len(modified_files)
            + len(deleted_files),
        }
