"""
定时任务调度器
负责定时扫描代码库差异并更新索引
"""

import asyncio
import threading
import time
from datetime import datetime, timedelta
from typing import Optional, Callable
from index_manager.incremental_updater import IncrementalUpdater
import logging

logger = logging.getLogger(__name__)


class IndexScheduler:
    """索引调度器"""

    def __init__(self, incremental_updater: IncrementalUpdater, repo_path: str):
        self.incremental_updater = incremental_updater
        self.repo_path = repo_path
        self.scan_interval = 300  # 默认5分钟扫描一次
        self.is_running = False
        self.scheduler_thread = None
        self.last_scan_time = None
        self.on_update_callback: Optional[Callable] = None

    def set_scan_interval(self, seconds: int):
        """设置扫描间隔（秒）"""
        self.scan_interval = max(60, seconds)  # 最小1分钟

    def set_update_callback(self, callback: Callable):
        """设置更新回调函数"""
        self.on_update_callback = callback

    def start(self):
        """启动定时扫描"""
        if self.is_running:
            return

        self.is_running = True
        self.scheduler_thread = threading.Thread(
            target=self._run_scheduler, daemon=True
        )
        self.scheduler_thread.start()
        logger.info(f"定时扫描已启动，间隔: {self.scan_interval}秒")

    def stop(self):
        """停止定时扫描"""
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        logger.info("定时扫描已停止")

    def _run_scheduler(self):
        """运行调度器"""
        while self.is_running:
            try:
                # 检查是否需要扫描
                if self._should_scan():
                    logger.debug(f"开始定时扫描: {datetime.now()}")

                    # 在新的事件循环中运行异步任务
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        # 执行增量更新
                        t0 = time.time()
                        update_stats = loop.run_until_complete(
                            self.incremental_updater.update_index(self.repo_path)
                        )

                        self.last_scan_time = datetime.now()

                        # 如果有更新，调用回调函数
                        if self._has_updates(update_stats):
                            if self.on_update_callback:
                                # 以关键字参数方式传递，避免回调签名不匹配
                                self.on_update_callback(update_stats=update_stats)
                            t1 = time.time()
                            logger.info(
                                f"增量更新完成: {update_stats}, 耗时: {t1 - t0:.2f}秒"
                            )
                        logger.debug(f"定时扫描完成: {update_stats}")
                    finally:
                        loop.close()

                # 等待下次扫描
                time.sleep(min(60, self.scan_interval))  # 最多等待60秒再检查

            except Exception as e:
                logger.error(f"定时扫描异常: {e}")
                time.sleep(60)  # 出错后等待1分钟再重试

    def _should_scan(self) -> bool:
        """判断是否应该进行扫描"""
        if self.last_scan_time is None:
            return True

        elapsed = (datetime.now() - self.last_scan_time).total_seconds()
        return elapsed >= self.scan_interval

    def _has_updates(self, update_stats: dict) -> bool:
        """判断是否有实际更新"""
        return (
            update_stats.get("added_chunks", 0) > 0
            or update_stats.get("modified_chunks", 0) > 0
            or update_stats.get("deleted_chunks", 0) > 0
        )

    async def force_scan(self) -> dict:
        """强制执行一次扫描"""
        logger.info("执行强制扫描...")

        update_stats = await self.incremental_updater.update_index(self.repo_path)
        self.last_scan_time = datetime.now()

        # 如果有更新，调用回调函数
        if self._has_updates(update_stats) and self.on_update_callback:
            # 以关键字参数方式传递，避免回调签名不匹配
            self.on_update_callback(update_stats=update_stats)

        logger.info(f"强制扫描完成: {update_stats}")
        return update_stats

    def get_status(self) -> dict:
        """获取调度器状态"""
        return {
            "is_running": self.is_running,
            "scan_interval": self.scan_interval,
            "last_scan_time": self.last_scan_time.isoformat()
            if self.last_scan_time
            else None,
            "next_scan_time": (
                self.last_scan_time + timedelta(seconds=self.scan_interval)
            ).isoformat()
            if self.last_scan_time
            else None,
        }
