"""
代码分片器
将解析后的代码按符号级粒度进行分片
"""

from typing import List, Dict, Any, Optional
from utils.interfaces import ICodeChunker
from utils.models import FileInfo, CodeChunk, ChunkType
from utils.helpers import generate_chunk_id, clean_code_content
from config import TREE_SITTER_CONFIG


class CodeChunker(ICodeChunker):
    """代码分片器实现"""

    def __init__(self):
        self.chunk_min_lines = TREE_SITTER_CONFIG["chunk_min_lines"]
        self.chunk_max_lines = TREE_SITTER_CONFIG["chunk_max_lines"]
        self.function_max_lines = TREE_SITTER_CONFIG.get("function_max_lines", 80)
        self.prefer_function_granularity = TREE_SITTER_CONFIG.get(
            "prefer_function_granularity", True
        )
        self.context_lines = TREE_SITTER_CONFIG["context_lines"]

    def chunk_by_symbols(
        self, parse_tree: object, file_info: FileInfo
    ) -> List[CodeChunk]:
        """按符号级粒度分片代码"""
        chunks = []

        if isinstance(parse_tree, dict):
            if parse_tree.get("type") in [
                "simple_parse",
                "tree_sitter_parse",
                "arkts_parse",
            ]:
                chunks = self._chunk_from_simple_parse(parse_tree, file_info)
            else:
                # 如果是其他格式的解析结果，降级到简单解析
                chunks = self._chunk_by_lines(file_info)
        else:
            # 如果不是字典格式的解析结果，降级到简单解析
            chunks = self._chunk_by_lines(file_info)

        return chunks

    def _chunk_from_simple_parse(
        self, parse_result: Dict[str, Any], file_info: FileInfo
    ) -> List[CodeChunk]:
        """从简单解析结果创建分片"""
        chunks = []
        symbols = parse_result["symbols"]
        lines = parse_result["lines"]

        if not symbols:
            # 如果没有识别到符号，按行分片
            return self._chunk_by_lines(file_info)

        # 首先验证和修正符号边界，确保没有重叠
        validated_symbols = self._validate_and_fix_symbol_boundaries(symbols, lines)

        # 如果启用函数粒度分片，优先处理函数和方法
        if self.prefer_function_granularity:
            chunks = self._chunk_by_function_granularity(
                validated_symbols, lines, file_info
            )
        else:
            chunks = self._chunk_by_original_logic(validated_symbols, lines, file_info)

        # 处理符号间的空隙（如果有重要内容）
        gap_chunks = self._create_gap_chunks(validated_symbols, lines, file_info)
        chunks.extend(gap_chunks)

        # 最终验证：确保没有重叠的分片
        chunks = self._remove_overlapping_chunks(chunks)

        # 按行号排序
        chunks.sort(key=lambda x: x.start_line)

        return chunks

    def _chunk_by_function_granularity(
        self, symbols: List[Dict], lines: List[str], file_info: FileInfo
    ) -> List[CodeChunk]:
        """按函数粒度分片代码"""
        chunks = []

        # 分离函数/方法符号和其他符号
        function_symbols = []
        other_symbols = []

        for symbol in symbols:
            if symbol["type"] in ["function_definition", "method_definition"]:
                function_symbols.append(symbol)
            else:
                other_symbols.append(symbol)

        # 处理函数/方法符号 - 每个函数作为独立分片
        for symbol in function_symbols:
            start_line = symbol["start_line"]
            end_line = symbol["end_line"]
            symbol_lines = lines[start_line - 1 : end_line]

            # 确保符号内容不为空
            if not any(line.strip() for line in symbol_lines):
                continue

            # 只有当函数非常长时才进行分割
            if len(symbol_lines) > self.function_max_lines:
                # 对于超长函数，尝试智能分割
                sub_chunks = self._split_large_function_carefully(
                    symbol_lines, start_line, symbol["name"], symbol["type"], file_info
                )
                chunks.extend(sub_chunks)
            else:
                # 整个函数作为一个分片
                chunk = self._create_chunk(
                    symbol_lines,
                    start_line,
                    end_line,
                    symbol["name"],
                    symbol["type"],
                    file_info,
                )
                if chunk:
                    chunks.append(chunk)

        # 处理其他符号（类、接口等）
        for symbol in other_symbols:
            start_line = symbol["start_line"]
            end_line = symbol["end_line"]
            symbol_lines = lines[start_line - 1 : end_line]

            # 确保符号内容不为空
            if not any(line.strip() for line in symbol_lines):
                continue

            if symbol["type"] in ["class_definition", "interface_definition"]:
                # 对于类，尝试按方法分割
                class_chunks = self._split_class_by_methods_enhanced(
                    symbol_lines, start_line, symbol["name"], file_info
                )
                if class_chunks:
                    chunks.extend(class_chunks)
                else:
                    # 如果无法按方法分割，整体处理
                    chunk = self._create_chunk(
                        symbol_lines,
                        start_line,
                        end_line,
                        symbol["name"],
                        symbol["type"],
                        file_info,
                    )
                    if chunk:
                        chunks.append(chunk)
            else:
                # 其他类型的符号
                chunk = self._create_chunk(
                    symbol_lines,
                    start_line,
                    end_line,
                    symbol["name"],
                    symbol["type"],
                    file_info,
                )
                if chunk:
                    chunks.append(chunk)

        return chunks

    def _chunk_by_original_logic(
        self, symbols: List[Dict], lines: List[str], file_info: FileInfo
    ) -> List[CodeChunk]:
        """原始的分片逻辑（向后兼容）"""
        chunks = []

        # 按符号创建分片
        for symbol in symbols:
            start_line = symbol["start_line"]
            end_line = symbol["end_line"]

            # 提取符号内容
            symbol_lines = lines[start_line - 1 : end_line]

            # 确保符号内容不为空
            if not any(line.strip() for line in symbol_lines):
                continue

            # 如果符号太长，需要智能分割（保持语法完整性）
            if len(symbol_lines) > self.chunk_max_lines:
                sub_chunks = self._split_large_symbol_intelligently(
                    symbol_lines, start_line, symbol["name"], symbol["type"], file_info
                )
                chunks.extend(sub_chunks)
            else:
                chunk = self._create_chunk(
                    symbol_lines,
                    start_line,
                    end_line,
                    symbol["name"],
                    symbol["type"],
                    file_info,
                )
                if chunk:
                    chunks.append(chunk)

        return chunks

    def _split_large_function_carefully(
        self,
        function_lines: List[str],
        start_line: int,
        function_name: str,
        function_type: str,
        file_info: FileInfo,
    ) -> List[CodeChunk]:
        """谨慎地分割超长函数，尽量保持逻辑完整性"""
        chunks = []

        # 对于超长函数，尝试找到逻辑分割点
        logical_breakpoints = self._find_logical_breakpoints(function_lines)

        if logical_breakpoints:
            # 按逻辑分割点分片
            current_start = 0
            part_index = 1

            for breakpoint in logical_breakpoints:
                if breakpoint > current_start:
                    chunk_lines = function_lines[current_start:breakpoint]
                    if len(chunk_lines) >= self.chunk_min_lines:
                        chunk = self._create_chunk(
                            chunk_lines,
                            start_line + current_start,
                            start_line + breakpoint - 1,
                            f"{function_name}_part_{part_index}",
                            function_type,
                            file_info,
                        )
                        if chunk:
                            chunks.append(chunk)
                            part_index += 1
                    current_start = breakpoint

            # 处理剩余部分
            if current_start < len(function_lines):
                chunk_lines = function_lines[current_start:]
                if len(chunk_lines) >= self.chunk_min_lines:
                    chunk = self._create_chunk(
                        chunk_lines,
                        start_line + current_start,
                        start_line + len(function_lines) - 1,
                        f"{function_name}_part_{part_index}",
                        function_type,
                        file_info,
                    )
                    if chunk:
                        chunks.append(chunk)
        else:
            # 如果无法找到逻辑分割点，按固定大小分割
            chunks = self._split_at_natural_boundaries(
                function_lines, start_line, function_name, function_type, file_info
            )

        return chunks if chunks else []

    def _find_logical_breakpoints(self, lines: List[str]) -> List[int]:
        """在函数中找到逻辑分割点"""
        breakpoints = []

        for i, line in enumerate(lines):
            stripped = line.strip()

            # 在控制结构结束后分割
            if stripped.startswith("}") and i > 0:
                next_line_idx = i + 1
                if next_line_idx < len(lines):
                    next_line = lines[next_line_idx].strip()
                    # 如果下一行不是立即的else、elif、catch等，则可以分割
                    if not next_line.startswith(
                        ("else", "elif", "catch", "finally", "}")
                    ):
                        breakpoints.append(i + 1)

            # 在注释块后分割
            elif stripped.startswith("//") or stripped.startswith("#"):
                if i > 0 and i + 1 < len(lines):
                    prev_line = lines[i - 1].strip()
                    next_line = lines[i + 1].strip()
                    if (
                        prev_line
                        and not prev_line.startswith(("//", "#"))
                        and next_line
                        and not next_line.startswith(("//", "#"))
                    ):
                        breakpoints.append(i + 1)

        # 过滤太近的分割点
        filtered_breakpoints = []
        for bp in breakpoints:
            if (
                not filtered_breakpoints
                or bp - filtered_breakpoints[-1] >= self.chunk_min_lines
            ):
                filtered_breakpoints.append(bp)

        return filtered_breakpoints

    def _split_class_by_methods_enhanced(
        self,
        class_lines: List[str],
        start_line: int,
        class_name: str,
        file_info: FileInfo,
    ) -> List[CodeChunk]:
        """增强版的按方法分割类"""
        chunks = []

        # 找到类中的所有方法
        methods = self._find_methods_in_class(class_lines)

        if not methods:
            return []  # 如果没有找到方法，返回空列表，交给调用者处理

        # 处理类头部（第一个方法之前的部分）
        if methods and methods[0]["start"] > 0:
            header_lines = class_lines[: methods[0]["start"]]
            if any(line.strip() for line in header_lines):
                chunk = self._create_chunk(
                    header_lines,
                    start_line,
                    start_line + methods[0]["start"] - 1,
                    f"{class_name}_header",
                    "class_definition",
                    file_info,
                )
                if chunk:
                    chunks.append(chunk)

        # 为每个方法创建分片
        for i, method in enumerate(methods):
            method_lines = class_lines[method["start"] : method["end"]]
            chunk = self._create_chunk(
                method_lines,
                start_line + method["start"],
                start_line + method["end"] - 1,
                f"{class_name}.{method['name']}",
                "method_definition",
                file_info,
            )
            if chunk:
                chunks.append(chunk)

        # 处理最后一个方法之后的部分
        last_method = methods[-1]
        if last_method["end"] < len(class_lines):
            footer_lines = class_lines[last_method["end"] :]
            if any(line.strip() for line in footer_lines):
                chunk = self._create_chunk(
                    footer_lines,
                    start_line + last_method["end"],
                    start_line + len(class_lines) - 1,
                    f"{class_name}_footer",
                    "class_definition",
                    file_info,
                )
                if chunk:
                    chunks.append(chunk)

        return chunks

    def _find_methods_in_class(self, class_lines: List[str]) -> List[Dict]:
        """在类中找到所有方法"""
        methods = []
        i = 0

        while i < len(class_lines):
            line = class_lines[i].strip()

            if self._looks_like_method_start(line):
                method_name = self._extract_method_name_from_line(line)
                method_start = i
                method_end = self._find_method_end_in_lines(class_lines, i)

                methods.append(
                    {"name": method_name, "start": method_start, "end": method_end + 1}
                )

                i = method_end + 1
            else:
                i += 1

        return methods

    def _find_symbol_end(
        self, symbols: List[Dict], current_symbol: Dict, total_lines: int
    ) -> int:
        """找到符号的结束行（降级方案）"""
        current_start = current_symbol["start_line"]

        # 找到下一个符号
        next_symbols = [s for s in symbols if s["start_line"] > current_start]
        if next_symbols:
            next_start = min(s["start_line"] for s in next_symbols)
            return next_start - 1
        else:
            return total_lines

    def _split_large_symbol_intelligently(
        self,
        symbol_lines: List[str],
        start_line: int,
        symbol_name: str,
        symbol_type: str,
        file_info: FileInfo,
    ) -> List[CodeChunk]:
        """智能分割过大的符号，保持语法完整性"""
        chunks = []

        # 对于类和接口，尝试按方法分割
        if symbol_type in ["class_definition", "interface_definition"]:
            method_chunks = self._split_class_by_methods(
                symbol_lines, start_line, symbol_name, file_info
            )
            if method_chunks:
                return method_chunks

        # 对于方法，尝试按逻辑块分割
        elif symbol_type in ["method_definition", "function_definition"]:
            block_chunks = self._split_method_by_blocks(
                symbol_lines, start_line, symbol_name, file_info
            )
            if block_chunks:
                return block_chunks

        # 降级到简单分割，但尽量在合适的位置断开
        return self._split_at_natural_boundaries(
            symbol_lines, start_line, symbol_name, symbol_type, file_info
        )

    def _split_class_by_methods(
        self,
        class_lines: List[str],
        start_line: int,
        class_name: str,
        file_info: FileInfo,
    ) -> List[CodeChunk]:
        """按方法分割类"""
        chunks = []
        current_chunk_lines = []
        current_chunk_start = start_line
        method_count = 0

        i = 0
        while i < len(class_lines):
            line = class_lines[i].strip()
            current_chunk_lines.append(class_lines[i])

            # 检查是否是方法开始
            if self._looks_like_method_start(line):
                # 如果当前块已经有内容，先保存
                if len(current_chunk_lines) > 1:  # 不只是当前行
                    chunk = self._create_chunk(
                        current_chunk_lines[:-1],
                        current_chunk_start,
                        current_chunk_start + len(current_chunk_lines) - 2,
                        f"{class_name}_part_{method_count}",
                        "class_definition",
                        file_info,
                    )
                    if chunk:
                        chunks.append(chunk)

                # 找到方法结束
                method_end = self._find_method_end_in_lines(class_lines, i)
                method_lines = class_lines[i : method_end + 1]

                # 创建方法块
                method_name = self._extract_method_name_from_line(line)
                chunk = self._create_chunk(
                    method_lines,
                    start_line + i,
                    start_line + method_end,
                    f"{class_name}.{method_name}",
                    "method_definition",
                    file_info,
                )
                if chunk:
                    chunks.append(chunk)

                # 重置当前块
                current_chunk_lines = []
                current_chunk_start = start_line + method_end + 1
                method_count += 1
                i = method_end + 1
                continue

            i += 1

        # 处理剩余内容
        if current_chunk_lines:
            chunk = self._create_chunk(
                current_chunk_lines,
                current_chunk_start,
                current_chunk_start + len(current_chunk_lines) - 1,
                f"{class_name}_part_{method_count}",
                "class_definition",
                file_info,
            )
            if chunk:
                chunks.append(chunk)

        return chunks if chunks else []

    def _split_method_by_blocks(
        self,
        method_lines: List[str],
        start_line: int,
        method_name: str,
        file_info: FileInfo,
    ) -> List[CodeChunk]:
        """按逻辑块分割方法"""
        # 对于方法，保持完整性更重要，只在必要时分割
        if len(method_lines) <= self.chunk_max_lines * 1.5:  # 不是特别大就不分割
            return []

        return self._split_at_natural_boundaries(
            method_lines, start_line, method_name, "method_definition", file_info
        )

    def _split_at_natural_boundaries(
        self,
        lines: List[str],
        start_line: int,
        symbol_name: str,
        symbol_type: str,
        file_info: FileInfo,
    ) -> List[CodeChunk]:
        """在自然边界处分割代码"""
        chunks = []
        current_start = 0
        part_index = 1

        while current_start < len(lines):
            # 计算当前块的大小
            current_end = min(current_start + self.chunk_max_lines, len(lines))

            # 如果不是最后一块，尝试在更好的位置断开
            if current_end < len(lines):
                # 向前查找合适的断点（空行、注释、右大括号等）
                for i in range(
                    current_end - 1,
                    max(current_start + self.chunk_min_lines, current_end - 20),
                    -1,
                ):
                    line = lines[i].strip()
                    if (
                        not line  # 空行
                        or line.startswith("//")  # 注释
                        or line == "}"  # 块结束
                        or line.endswith("};")
                    ):  # 语句结束
                        current_end = i + 1
                        break

            chunk_lines = lines[current_start:current_end]
            chunk_start_line = start_line + current_start
            chunk_end_line = start_line + current_end - 1

            chunk_symbol_name = f"{symbol_name}_part_{part_index}"

            chunk = self._create_chunk(
                chunk_lines,
                chunk_start_line,
                chunk_end_line,
                chunk_symbol_name,
                symbol_type,
                file_info,
            )

            if chunk:
                chunks.append(chunk)
                part_index += 1

            current_start = current_end

        return chunks

    def _create_gap_chunks(
        self, symbols: List[Dict], lines: List[str], file_info: FileInfo
    ) -> List[CodeChunk]:
        """创建符号间空隙的分片（如果包含重要内容）"""
        chunks = []

        if not symbols:
            # 如果没有符号，整个文件作为一个分片
            if lines and any(line.strip() for line in lines):
                chunk = self._create_chunk(
                    lines, 1, len(lines), "file_content", "other", file_info
                )
                if chunk:
                    chunks.append(chunk)
            return chunks

        # 排序符号
        sorted_symbols = sorted(symbols, key=lambda x: x["start_line"])

        # 检查文件开头到第一个符号之间的内容
        first_symbol = sorted_symbols[0]
        if first_symbol["start_line"] > 1:
            gap_start = 1
            gap_end = first_symbol["start_line"] - 1
            gap_lines = lines[gap_start - 1 : gap_end]

            # 检查是否包含重要内容
            if self._has_important_content(gap_lines):
                chunk = self._create_chunk(
                    gap_lines, gap_start, gap_end, "file_header", "other", file_info
                )
                if chunk:
                    chunks.append(chunk)

        # 检查符号之间的空隙
        for i in range(len(sorted_symbols) - 1):
            current_symbol = sorted_symbols[i]
            next_symbol = sorted_symbols[i + 1]

            gap_start = current_symbol["end_line"] + 1
            gap_end = next_symbol["start_line"] - 1

            # 如果空隙有内容且不为空
            if gap_start <= gap_end:
                gap_lines = lines[gap_start - 1 : gap_end]

                # 检查是否包含重要内容
                if self._has_important_content(gap_lines):
                    chunk = self._create_chunk(
                        gap_lines,
                        gap_start,
                        gap_end,
                        f"gap_between_symbols_{i}",
                        "other",
                        file_info,
                    )
                    if chunk:
                        chunks.append(chunk)

        # 检查最后一个符号之后的内容
        last_symbol = sorted_symbols[-1]
        if last_symbol["end_line"] < len(lines):
            gap_start = last_symbol["end_line"] + 1
            gap_end = len(lines)
            gap_lines = lines[gap_start - 1 : gap_end]

            # 检查是否包含重要内容
            if self._has_important_content(gap_lines):
                chunk = self._create_chunk(
                    gap_lines, gap_start, gap_end, "file_footer", "other", file_info
                )
                if chunk:
                    chunks.append(chunk)

        return chunks

    def _has_important_content(self, lines: List[str]) -> bool:
        """检查行列表是否包含重要内容"""
        for line in lines:
            stripped = line.strip()
            if (
                stripped
                and not stripped.startswith("//")
                and not stripped.startswith("/*")
                and stripped != "*/"
                and not stripped.startswith("*")
            ):
                return True
        return False

    def _looks_like_method_start(self, line: str) -> bool:
        """检查是否看起来像方法开始"""
        if not line or line.startswith("//"):
            return False
        return (
            "(" in line and ")" in line and ("{" in line or line.rstrip().endswith("{"))
        )

    def _extract_method_name_from_line(self, line: str) -> str:
        """从行中提取方法名"""
        try:
            paren_pos = line.find("(")
            if paren_pos == -1:
                return "unknown"

            before_paren = line[:paren_pos].strip()
            parts = before_paren.split()
            if parts:
                return parts[-1]
            return "unknown"
        except:
            return "unknown"

    def _find_method_end_in_lines(self, lines: List[str], start_idx: int) -> int:
        """在行列表中找到方法结束位置"""
        brace_count = 0
        found_opening = False

        for i in range(start_idx, len(lines)):
            line = lines[i]

            for char in line:
                if char == "{":
                    brace_count += 1
                    found_opening = True
                elif char == "}":
                    brace_count -= 1
                    if found_opening and brace_count == 0:
                        return i

        return len(lines) - 1

    def _validate_and_fix_symbol_boundaries(
        self, symbols: List[Dict], lines: List[str]
    ) -> List[Dict]:
        """验证和修正符号边界，确保没有重叠"""
        if not symbols:
            return symbols

        # 按开始行排序
        sorted_symbols = sorted(symbols, key=lambda x: x["start_line"])
        validated_symbols = []

        for i, symbol in enumerate(sorted_symbols):
            start_line = symbol["start_line"]

            # 如果符号已经有end_line，使用它；否则计算
            if "end_line" in symbol and symbol["end_line"] > start_line:
                end_line = symbol["end_line"]
            else:
                # 重新计算结束行
                end_line = self._calculate_symbol_end_line(
                    symbol, lines, start_line - 1
                )

            # 检查与下一个符号的冲突
            if i + 1 < len(sorted_symbols):
                next_symbol = sorted_symbols[i + 1]
                next_start = next_symbol["start_line"]

                # 如果当前符号的结束行超过了下一个符号的开始行，需要调整
                if end_line >= next_start:
                    # 将结束行调整到下一个符号开始的前一行
                    end_line = next_start - 1

                    # 确保至少包含符号的开始行
                    if end_line < start_line:
                        end_line = start_line

            # 确保不超过文件末尾
            end_line = min(end_line, len(lines))

            # 创建修正后的符号
            validated_symbol = symbol.copy()
            validated_symbol["end_line"] = end_line
            validated_symbols.append(validated_symbol)

        return validated_symbols

    def _calculate_symbol_end_line(
        self, symbol: Dict, lines: List[str], start_idx: int
    ) -> int:
        """重新计算符号的结束行"""
        symbol_type = symbol.get("type", "")

        if symbol_type in ["method_definition", "function_definition"]:
            return self._find_method_end_in_lines(lines, start_idx) + 1
        elif symbol_type in ["class_definition", "interface_definition"]:
            return self._find_block_end_in_lines(lines, start_idx) + 1
        else:
            # 对于其他类型，默认只包含当前行
            return start_idx + 1

    def _find_block_end_in_lines(self, lines: List[str], start_idx: int) -> int:
        """在行列表中找到代码块的结束行"""
        brace_count = 0
        found_opening = False

        for i in range(start_idx, len(lines)):
            line = lines[i]

            # 跳过字符串和注释中的括号
            in_string = False
            in_comment = False
            j = 0

            while j < len(line):
                char = line[j]

                # 处理字符串
                if char in ['"', "'", "`"] and not in_comment:
                    if not in_string:
                        in_string = True
                    elif in_string:
                        in_string = False

                # 处理注释
                elif (
                    char == "/"
                    and j + 1 < len(line)
                    and line[j + 1] == "/"
                    and not in_string
                ):
                    in_comment = True
                    break

                # 计算大括号（只在非字符串、非注释中）
                elif not in_string and not in_comment:
                    if char == "{":
                        brace_count += 1
                        found_opening = True
                    elif char == "}":
                        brace_count -= 1

                        # 如果找到了开始括号，且括号计数回到0，说明块结束了
                        if found_opening and brace_count == 0:
                            return i

                j += 1

        return len(lines) - 1

    def _remove_overlapping_chunks(self, chunks: List[CodeChunk]) -> List[CodeChunk]:
        """移除重叠的分片"""
        if not chunks:
            return chunks

        # 按开始行排序
        sorted_chunks = sorted(chunks, key=lambda x: x.start_line)
        non_overlapping_chunks = []

        for chunk in sorted_chunks:
            # 检查是否与已有分片重叠
            overlaps = False
            for existing_chunk in non_overlapping_chunks:
                if self._chunks_overlap(chunk, existing_chunk):
                    overlaps = True
                    # 如果重叠，选择更完整的那个（通常是更长的）
                    if (chunk.end_line - chunk.start_line) > (
                        existing_chunk.end_line - existing_chunk.start_line
                    ):
                        # 移除较短的分片，添加较长的分片
                        non_overlapping_chunks.remove(existing_chunk)
                        non_overlapping_chunks.append(chunk)
                    break

            if not overlaps:
                non_overlapping_chunks.append(chunk)

        return non_overlapping_chunks

    def _chunks_overlap(self, chunk1: CodeChunk, chunk2: CodeChunk) -> bool:
        """检查两个分片是否重叠"""
        # 检查文件路径是否相同
        if chunk1.file_path != chunk2.file_path:
            return False

        # 检查行号范围是否重叠
        return not (
            chunk1.end_line < chunk2.start_line or chunk2.end_line < chunk1.start_line
        )

    def _split_large_symbol(
        self,
        symbol_lines: List[str],
        start_line: int,
        symbol_name: str,
        symbol_type: str,
        file_info: FileInfo,
    ) -> List[CodeChunk]:
        """分割过大的符号"""
        chunks = []
        current_start = 0

        while current_start < len(symbol_lines):
            current_end = min(current_start + self.chunk_max_lines, len(symbol_lines))

            chunk_lines = symbol_lines[current_start:current_end]
            chunk_start_line = start_line + current_start
            chunk_end_line = start_line + current_end - 1

            # 为分割的块添加后缀
            chunk_symbol_name = (
                f"{symbol_name}_part_{current_start // self.chunk_max_lines + 1}"
            )

            chunk = self._create_chunk(
                chunk_lines,
                chunk_start_line,
                chunk_end_line,
                chunk_symbol_name,
                symbol_type,
                file_info,
            )

            if chunk:
                chunks.append(chunk)

            current_start = current_end

        return chunks

    def _chunk_by_lines(self, file_info: FileInfo) -> List[CodeChunk]:
        """按行数分片（降级方案）"""
        chunks = []
        lines = file_info.content.split("\n")

        current_start = 0
        chunk_index = 1

        while current_start < len(lines):
            current_end = min(current_start + self.chunk_max_lines, len(lines))

            chunk_lines = lines[current_start:current_end]

            # 跳过空白分片
            if not any(line.strip() for line in chunk_lines):
                current_start = current_end
                continue

            chunk = self._create_chunk(
                chunk_lines,
                current_start + 1,
                current_end,
                f"chunk_{chunk_index}",
                "other",
                file_info,
            )

            if chunk:
                chunks.append(chunk)
                chunk_index += 1

            current_start = current_end

        return chunks

    def _create_chunk(
        self,
        lines: List[str],
        start_line: int,
        end_line: int,
        symbol_name: str,
        symbol_type: str,
        file_info: FileInfo,
    ) -> Optional[CodeChunk]:
        """创建代码分片"""
        content = "\n".join(lines)

        # 清理内容
        cleaned_content = clean_code_content(content)

        # 跳过空内容
        if not cleaned_content.strip():
            return None

        # 生成唯一ID
        chunk_id = generate_chunk_id(
            file_info.relative_path, start_line, end_line, symbol_name
        )

        # 确定分片类型
        chunk_type = self._determine_chunk_type(symbol_type)

        return CodeChunk(
            id=chunk_id,
            file_path=file_info.file_path,
            relative_path=file_info.relative_path,
            start_line=start_line,
            end_line=end_line,
            content=cleaned_content,
            chunk_type=chunk_type,
            symbol_name=symbol_name,
            language=file_info.language,
        )

    def _determine_chunk_type(self, symbol_type: str) -> str:
        """确定分片类型"""
        type_mapping = {
            "function_definition": ChunkType.FUNCTION.value,
            "class_definition": ChunkType.CLASS.value,
            "method_definition": ChunkType.METHOD.value,
            "interface_declaration": ChunkType.INTERFACE.value,
            "other": ChunkType.OTHER.value,
        }

        return type_mapping.get(symbol_type, ChunkType.OTHER.value)

    def chunk_markdown(self, file_info: FileInfo) -> List[CodeChunk]:
        """分片Markdown文件"""
        chunks = []
        lines = file_info.content.split("\n")

        # 查找标题行
        headers = []
        for i, line in enumerate(lines):
            if line.strip().startswith("#"):
                level = len(line) - len(line.lstrip("#"))
                title = line.strip("#").strip()
                headers.append({"line": i + 1, "level": level, "title": title})

        if not headers:
            # 如果没有标题，按段落分片
            return self._chunk_markdown_by_paragraphs(file_info)

        # 按标题分片
        for i, header in enumerate(headers):
            start_line = header["line"]

            # 确定结束行
            if i + 1 < len(headers):
                end_line = headers[i + 1]["line"] - 1
            else:
                end_line = len(lines)

            # 提取内容
            section_lines = lines[start_line - 1 : end_line]

            # 如果章节太长，需要分割
            if len(section_lines) > self.chunk_max_lines:
                sub_chunks = self._split_markdown_section(
                    section_lines, start_line, header["title"], file_info
                )
                chunks.extend(sub_chunks)
            else:
                chunk = self._create_markdown_chunk(
                    section_lines, start_line, end_line, header["title"], file_info
                )
                if chunk:
                    chunks.append(chunk)

        return chunks

    def _chunk_markdown_by_paragraphs(self, file_info: FileInfo) -> List[CodeChunk]:
        """按段落分片Markdown"""
        chunks = []
        lines = file_info.content.split("\n")

        current_paragraph = []
        current_start_line = 1
        chunk_index = 1

        for i, line in enumerate(lines):
            if line.strip() == "":
                # 空行，结束当前段落
                if current_paragraph:
                    chunk = self._create_markdown_chunk(
                        current_paragraph,
                        current_start_line,
                        i,
                        f"paragraph_{chunk_index}",
                        file_info,
                    )
                    if chunk:
                        chunks.append(chunk)
                        chunk_index += 1

                    current_paragraph = []
                    current_start_line = i + 2
            else:
                current_paragraph.append(line)

        # 处理最后一个段落
        if current_paragraph:
            chunk = self._create_markdown_chunk(
                current_paragraph,
                current_start_line,
                len(lines),
                f"paragraph_{chunk_index}",
                file_info,
            )
            if chunk:
                chunks.append(chunk)

        return chunks

    def _split_markdown_section(
        self, section_lines: List[str], start_line: int, title: str, file_info: FileInfo
    ) -> List[CodeChunk]:
        """分割过大的Markdown章节"""
        chunks = []
        current_start = 0
        part_index = 1

        while current_start < len(section_lines):
            current_end = min(current_start + self.chunk_max_lines, len(section_lines))

            chunk_lines = section_lines[current_start:current_end]
            chunk_start_line = start_line + current_start
            chunk_end_line = start_line + current_end - 1

            chunk_title = f"{title}_part_{part_index}"

            chunk = self._create_markdown_chunk(
                chunk_lines, chunk_start_line, chunk_end_line, chunk_title, file_info
            )

            if chunk:
                chunks.append(chunk)
                part_index += 1

            current_start = current_end

        return chunks

    def _create_markdown_chunk(
        self,
        lines: List[str],
        start_line: int,
        end_line: int,
        title: str,
        file_info: FileInfo,
    ) -> Optional[CodeChunk]:
        """创建Markdown分片"""
        content = "\n".join(lines)

        # 跳过空内容
        if not content.strip():
            return None

        # 生成唯一ID
        chunk_id = generate_chunk_id(
            file_info.relative_path, start_line, end_line, title
        )

        return CodeChunk(
            id=chunk_id,
            file_path=file_info.file_path,
            relative_path=file_info.relative_path,
            start_line=start_line,
            end_line=end_line,
            content=content,
            chunk_type=ChunkType.MARKDOWN_SECTION.value,
            symbol_name=title,
            language=file_info.language,
        )
