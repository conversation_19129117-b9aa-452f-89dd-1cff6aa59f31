"""
ArkTS Tree-sitter解析器
专门用于解析ArkTS (.ets) 文件
"""

import os
import platform
from pathlib import Path
from typing import Optional, Dict, Any, List
import logging

logger = logging.getLogger(__name__)

try:
    from tree_sitter import Language, Parser

    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False


def get_system_and_arch():
    """获取系统和架构信息"""
    system = platform.system().lower()
    if system == "darwin":
        system = "mac"

    machine = platform.machine().lower()
    if machine in ["x86_64", "amd64"]:
        arch = "x64"
    elif machine in ["arm64", "aarch64"]:
        arch = "arm64"
    else:
        arch = "x64"  # 默认

    return system, arch


class ArkTSParser:
    """ArkTS专用解析器"""

    def __init__(self):
        self.parser = None
        self.language = None
        self._initialized = False

    def _init_arkts_parser(self):
        """初始化ArkTS解析器"""
        if self._initialized:
            return

        self._initialized = True

        if not TREE_SITTER_AVAILABLE:
            logger.warning("tree-sitter库未安装，ArkTS解析器将使用简单文本解析")
            return

        try:
            system, arch = get_system_and_arch()

            so_path_map = {
                ("linux", "arm64"): "libarkts_parser_linux_arm64.so",
                ("linux", "x64"): "libarkts_parser_linux_x64.so",
                ("mac", "arm64"): "libarkts_parser_mac_arm64.so",
                ("windows", "x64"): "libarkts_parser_win_x64.dll",
            }

            if (system, arch) not in so_path_map:
                logger.warning(
                    f"不支持的系统架构 {system}-{arch}，ArkTS解析器将使用简单文本解析"
                )
                return

            # 构建.so文件路径
            so_file = so_path_map[(system, arch)]
            so_path = os.path.join(
                Path(__file__).parent.parent, "bin", "arkts_tree_sitter", so_file
            )

            if not os.path.exists(so_path):
                logger.warning(f"ArkTS解析器库文件不存在: {so_path}")
                logger.info("ArkTS解析器将使用简单文本解析")
                return

            # 加载ArkTS语言
            # 你的语言加载逻辑看起来很健壮，试图兼容多种情况，这里保持不变
            language_loaded = False

            # 方式1: 尝试指针+名称（兼容0.21.3版本）
            try:
                import ctypes

                lib = ctypes.CDLL(so_path)
                arkts_func = lib.tree_sitter_arkts
                arkts_func.restype = ctypes.c_void_p
                ptr = arkts_func()
                self.language = Language(ptr, "arkts")
                language_loaded = True
            except Exception as e:
                logger.warning(f"方式1加载ArkTS解析器初始化失败: {e}")
                pass

            # 方式2: 尝试指针+名称
            try:
                if not language_loaded:
                    import ctypes

                    lib = ctypes.CDLL(so_path)
                    arkts_func = lib.tree_sitter_arkts
                    arkts_func.restype = ctypes.c_void_p
                    ptr = arkts_func()
                    self.language = Language(
                        ptr, "arkts"
                    )  # 这个方式可能在某些旧版中需要
                    language_loaded = True
            except Exception as e:
                logger.warning(f"方式2加载ArkTS解析器初始化失败: {e}")
                pass

            # 方式3: 回退到旧API
            if not language_loaded:
                self.language = Language(so_path, "arkts")

            # 创建解析器并设置语言
            self.parser = Parser()
            self.parser.set_language(self.language)

            logger.info(f"ArkTS Tree-sitter解析器已加载: {so_file}")

        except Exception as e:
            logger.exception(f"ArkTS解析器初始化失败: {e}")
            logger.info("ArkTS解析器将使用简单文本解析")
            self.parser = None
            self.language = None

    def is_available(self) -> bool:
        """检查ArkTS解析器是否可用"""
        if not self._initialized:
            self._init_arkts_parser()
        return self.parser is not None and self.language is not None

    def parse(self, content: str) -> Optional[object]:
        """解析ArkTS代码"""
        if not self.is_available():
            return None

        try:
            tree = self.parser.parse(bytes(content, "utf-8"))
            return tree
        except Exception as e:
            logger.warning(f"ArkTS解析失败: {e}")
            return None

    def extract_symbols(self, tree) -> List[Dict[str, Any]]:
        """从ArkTS解析树中提取符号"""
        if not tree:
            return []

        symbols = []

        def traverse_node(node, depth=0):
            """遍历AST节点"""
            # ArkTS的主要符号类型
            symbol_types = {
                "struct_declaration": "class",
                "class_declaration": "class",
                "interface_declaration": "interface",
                "function_declaration": "function",
                "method_definition": "method",
                "constructor_definition": "method",
                "property_declaration": "property",
            }

            if node.type in symbol_types:
                symbol_info = {
                    "type": f"{symbol_types[node.type]}_definition",
                    "name": self._extract_symbol_name(node),
                    "start_line": node.start_point[0] + 1,
                    "end_line": node.end_point[0] + 1,
                    "start_byte": node.start_byte,
                    "end_byte": node.end_byte,
                    "node_type": node.type,
                }
                symbols.append(symbol_info)

            # 递归遍历子节点
            for child in node.children:
                traverse_node(child, depth + 1)

        traverse_node(tree.root_node)
        return symbols

    def _extract_symbol_name(self, node) -> str:
        """从AST节点中提取符号名称"""

        # 尝试找到标识符子节点
        def find_identifier(node, depth=0):
            """递归查找标识符节点"""
            if depth > 3:  # 限制递归深度
                return None

            if node.type in ["identifier", "property_identifier", "type_identifier"]:
                return node.text.decode("utf-8")

            # 在子节点中查找
            for child in node.children:
                result = find_identifier(child, depth + 1)
                if result:
                    return result
            return None

        name = find_identifier(node)
        if name:
            return name

        # 如果找不到标识符，返回默认名称
        return f"unnamed_{node.type}"

    def parse_simple(self, content: str) -> List[Dict[str, Any]]:
        """简单文本解析ArkTS代码（降级方案）"""
        lines = content.split("\n")
        symbols = []

        i = 0
        while i < len(lines):
            line = lines[i]
            stripped = line.strip()

            # struct定义
            if stripped.startswith("struct ") and "{" in stripped:
                try:
                    struct_name = (
                        stripped.split("struct ")[1].split(" ")[0].split("{")[0].strip()
                    )
                    end_line = self._find_block_end(lines, i)
                    symbols.append(
                        {
                            "type": "class_definition",
                            "name": struct_name,
                            "start_line": i + 1,
                            "end_line": end_line,
                            "line": line,
                        }
                    )
                except IndexError:
                    pass

            # class定义
            elif stripped.startswith("class ") and "{" in stripped:
                try:
                    class_name = (
                        stripped.split("class ")[1].split(" ")[0].split("{")[0].strip()
                    )
                    end_line = self._find_block_end(lines, i)
                    symbols.append(
                        {
                            "type": "class_definition",
                            "name": class_name,
                            "start_line": i + 1,
                            "end_line": end_line,
                            "line": line,
                        }
                    )
                except IndexError:
                    pass

            # interface定义
            elif stripped.startswith("interface ") and "{" in stripped:
                try:
                    interface_name = (
                        stripped.split("interface ")[1]
                        .split(" ")[0]
                        .split("{")[0]
                        .strip()
                    )
                    end_line = self._find_block_end(lines, i)
                    symbols.append(
                        {
                            "type": "interface_definition",
                            "name": interface_name,
                            "start_line": i + 1,
                            "end_line": end_line,
                            "line": line,
                        }
                    )
                except IndexError:
                    pass

            # 函数定义
            elif "function " in stripped and "(" in stripped:
                try:
                    func_part = stripped.split("function ")[1]
                    func_name = func_part.split("(")[0].strip()
                    end_line = self._find_function_end(lines, i)
                    symbols.append(
                        {
                            "type": "function_definition",
                            "name": func_name,
                            "start_line": i + 1,
                            "end_line": end_line,
                            "line": line,
                        }
                    )
                except IndexError:
                    pass

            # 方法定义（不包含function关键字）
            elif self._is_method_definition(stripped):
                try:
                    method_name = self._extract_method_name(stripped)
                    if method_name:
                        end_line = self._find_function_end(lines, i)
                        symbols.append(
                            {
                                "type": "method_definition",
                                "name": method_name,
                                "start_line": i + 1,
                                "end_line": end_line,
                                "line": line,
                            }
                        )
                except IndexError:
                    pass

            i += 1

        return symbols

    def _is_method_definition(self, line: str) -> bool:
        """判断是否是方法定义"""
        if not line or line.startswith("//") or line.startswith("/*"):
            return False

        # 必须包含括号
        if "(" not in line or ")" not in line:
            return False

        # 排除控制流语句
        control_keywords = ["if", "for", "while", "switch", "catch", "return"]
        for keyword in control_keywords:
            if line.strip().startswith(keyword + " ") or line.strip().startswith(
                keyword + "("
            ):
                return False

        # 排除变量声明和赋值
        if "=" in line and "==" not in line and "!=" not in line and "=>" not in line:
            equals_pos = line.find("=")
            paren_pos = line.find("(")
            if equals_pos < paren_pos:  # 等号在括号前面，可能是变量赋值
                return False

        # 必须有大括号开始（方法体）
        return "{" in line or line.rstrip().endswith("{")

    def _extract_method_name(self, line: str) -> str:
        """从方法定义行提取方法名"""
        try:
            # 找到括号前的部分
            paren_pos = line.find("(")
            if paren_pos == -1:
                return ""

            before_paren = line[:paren_pos].strip()

            # 分割并取最后一个标识符
            parts = before_paren.split()
            if not parts:
                return ""

            method_name = parts[-1]

            # 清理可能的修饰符
            method_name = method_name.split(":")[-1]  # 移除类型注解
            method_name = method_name.split(".")[-1]  # 移除对象引用

            return method_name
        except:
            return ""

    def _find_block_end(self, lines: List[str], start_line: int) -> int:
        """找到代码块的结束行（用于class, struct, interface）"""
        brace_count = 0
        found_opening = False

        for i in range(start_line, len(lines)):
            line = lines[i]

            # 计算大括号
            for char in line:
                if char == "{":
                    brace_count += 1
                    found_opening = True
                elif char == "}":
                    brace_count -= 1

                    # 如果找到了开始括号，且括号计数回到0，说明块结束了
                    if found_opening and brace_count == 0:
                        return i + 1

        # 如果没找到结束，返回文件末尾
        return len(lines)

    def _find_function_end(self, lines: List[str], start_line: int) -> int:
        """找到函数/方法的结束行"""
        # 首先检查是否是单行函数（箭头函数等）
        start_line_content = lines[start_line].strip()
        if (
            "=>" in start_line_content
            and start_line_content.count("{") == start_line_content.count("}")
            and start_line_content.endswith(";")
        ):
            return start_line + 1

        brace_count = 0
        found_opening = False

        for i in range(start_line, len(lines)):
            line = lines[i]

            # 跳过字符串和注释中的括号
            in_string = False
            in_comment = False
            j = 0

            while j < len(line):
                char = line[j]

                # 处理字符串
                if char in ['"', "'", "`"] and not in_comment:
                    if not in_string:
                        in_string = True
                    elif in_string:
                        in_string = False

                # 处理注释
                elif (
                    char == "/"
                    and j + 1 < len(line)
                    and line[j + 1] == "/"
                    and not in_string
                ):
                    in_comment = True
                    break

                # 计算大括号（只在非字符串、非注释中）
                elif not in_string and not in_comment:
                    if char == "{":
                        brace_count += 1
                        found_opening = True
                    elif char == "}":
                        brace_count -= 1

                        # 如果找到了开始括号，且括号计数回到0，说明函数结束了
                        if found_opening and brace_count == 0:
                            return i + 1

                j += 1

        # 如果没找到结束，返回文件末尾
        return len(lines)


# 全局ArkTS解析器实例
arkts_parser = ArkTSParser()
