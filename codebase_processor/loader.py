"""
代码库加载器
负责加载和过滤代码库中的文件
"""

import os
import fnmatch
from pathlib import Path
from typing import List, Set
from config import SUPPORTED_LANGUAGES, ignore_config
from utils.interfaces import ICodebaseLoader
from utils.models import FileInfo
from utils.helpers import (
    get_file_language,
    calculate_file_crc32,
    is_text_file,
    normalize_path,
    get_relative_path,
)
from utils.file_oper import read_file
import logging

logger = logging.getLogger(__name__)


class CodebaseLoader(ICodebaseLoader):
    """代码库加载器实现"""

    def __init__(self):
        self.supported_extensions = self._get_supported_extensions()
        # 使用配置文件中的忽略模式
        self.ignore_patterns = ignore_config.all_ignore_patterns

    def _get_supported_extensions(self) -> Set[str]:
        """获取所有支持的文件扩展名"""
        extensions = set()
        for lang_extensions in SUPPORTED_LANGUAGES.values():
            extensions.update(lang_extensions)
        return extensions

    def load_files(self, repo_path: str) -> List[FileInfo]:
        """加载指定目录下的所有支持的文件"""
        repo_path = normalize_path(os.path.abspath(repo_path))

        if not os.path.exists(repo_path):
            raise FileNotFoundError(f"仓库路径不存在: {repo_path}")

        if not os.path.isdir(repo_path):
            raise NotADirectoryError(f"路径不是目录: {repo_path}")

        file_infos = []

        for root, dirs, files in os.walk(repo_path):
            # 过滤忽略的目录
            dirs[:] = [
                d
                for d in dirs
                if not self._should_ignore_path(os.path.join(root, d), repo_path)
            ]

            for file in files:
                file_path = normalize_path(os.path.join(root, file))

                # 检查是否应该忽略此文件
                if self._should_ignore_path(file_path, repo_path):
                    continue

                # 检查是否为支持的文件类型
                if not self._is_supported_file(file_path):
                    continue

                # 检查是否为文本文件
                if not is_text_file(file_path):
                    continue

                try:
                    file_info = self._create_file_info(file_path, repo_path)
                    if file_info:
                        file_infos.append(file_info)
                except Exception as e:
                    logger.warning(f"无法加载文件 {file_path}: {e}")
                    continue

        return file_infos

    def filter_supported_files(self, files: List[str]) -> List[str]:
        """过滤出支持的文件类型"""
        supported_files = []

        for file_path in files:
            if self._is_supported_file(file_path):
                supported_files.append(file_path)

        return supported_files

    def _should_ignore_path(self, path: str, repo_path: str) -> bool:
        """检查路径是否应该被忽略"""
        # 获取相对路径
        try:
            relative_path = get_relative_path(path, repo_path)
        except:
            relative_path = os.path.relpath(path, repo_path)

        # 检查每个忽略模式
        for pattern in self.ignore_patterns:
            # 使用fnmatch进行模式匹配
            if fnmatch.fnmatch(relative_path, pattern) or fnmatch.fnmatch(
                os.path.basename(path), pattern
            ):
                return True

            # 检查路径的任何部分是否匹配模式
            path_parts = relative_path.split(os.sep)
            for part in path_parts:
                if fnmatch.fnmatch(part, pattern.replace("**/", "").replace("/**", "")):
                    return True

        return False

    def _is_supported_file(self, file_path: str) -> bool:
        """检查文件是否为支持的类型"""
        file_ext = Path(file_path).suffix.lower()
        return file_ext in self.supported_extensions

    def _create_file_info(self, file_path: str, repo_path: str) -> FileInfo:
        """创建文件信息对象"""
        content = read_file(file_path)
        if content is None:
            return None

        language = get_file_language(file_path)
        if not language:
            return None

        relative_path = get_relative_path(file_path, repo_path)
        file_size = len(content.encode("utf-8"))
        crc32 = calculate_file_crc32(file_path)

        return FileInfo(
            file_path=file_path,
            relative_path=relative_path,
            language=language,
            content=content,
            size=file_size,
            crc32=crc32,
        )

    def get_file_stats(self, repo_path: str) -> dict:
        """获取仓库文件统计信息"""
        file_infos = self.load_files(repo_path)

        stats = {
            "total_files": len(file_infos),
            "total_size": sum(info.size for info in file_infos),
            "languages": {},
            "largest_file": None,
            "smallest_file": None,
        }

        # 按语言统计
        for info in file_infos:
            if info.language not in stats["languages"]:
                stats["languages"][info.language] = {"count": 0, "size": 0}
            stats["languages"][info.language]["count"] += 1
            stats["languages"][info.language]["size"] += info.size

        # 找出最大和最小文件
        if file_infos:
            stats["largest_file"] = max(file_infos, key=lambda x: x.size)
            stats["smallest_file"] = min(file_infos, key=lambda x: x.size)

        return stats
