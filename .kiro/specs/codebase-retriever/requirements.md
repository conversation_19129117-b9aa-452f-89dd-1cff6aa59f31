# 需求文档

## 介绍

codebase_retriever是一个智能代码库检索工具，旨在提供类似Cursor Composer的精准代码检索能力。该工具通过预处理代码库，生成代码洞察和嵌入向量，然后结合ripgrep和LLM筛选，为用户查询提供高精度的代码片段检索服务。

## 需求

### 需求1：代码库预处理和索引

**用户故事：** 作为开发者，我希望工具能够在启动时自动加载和处理整个代码库，以便后续能够进行快速精准的检索。

#### 验收标准

1. 当工具启动时，系统应当加载repo-path参数指定目录中的所有代码文件和markdown文件
2. 当处理代码文件时，系统应当使用tree-sitter按照符号级粒度进行有意义的分片
3. 当分片完成后，系统应当为每个分片生成code insight（使用LLM）
4. 当生成insight后，系统应当计算每个分片的embedding向量
5. 当处理分片时，系统应当提取关键词列表（函数名、类名、全局变量、文件路径等）
6. 系统应当支持Python、JavaScript、TypeScript、Java、C、C++、Rust、Markdown等语言
7. 系统应当支持定时扫描代码库差异，刷新分片构建，分片洞察、关键字列表、embedding索引

### 需求2：查询处理和重写

**用户故事：** 作为开发者，我希望能够使用自然语言查询代码库，工具能够智能地将我的查询转换为有效的搜索命令。

#### 验收标准

1. 当接收到用户查询时，系统应当结合repo_insight+关键词列表将查询重写为一个或多个ripgrep命令
2. 当查询重写完成后，系统应当执行ripgrep操作找到对应的分片列表
3. 当找到分片后，系统应当调用LLM检查分片是否满足查询需求
4. 如果没有找到合适的分片，系统应当将查询转换为适用于embedding检索的输入
5. 当进行embedding检索时，系统应当找到相关分片并进行LLM筛选

### 需求3：高精度检索结果

**用户故事：** 作为开发者，我希望检索结果具有很高的精确度，能够准确找到我需要的代码片段。

#### 验收标准

1. 当评测时，系统的P@1指标应当大于80%
2. 当评测时，系统的MRR@K指标应当大于70%
3. 系统应当尽可能减小K值以提高检索效率
4. 当输出结果时，系统应当只返回经过筛选的高质量代码/文档分片

### 需求4：清晰的项目架构

**用户故事：** 作为开发者，我希望项目代码结构清晰优雅，便于维护和扩展。

#### 验收标准

1. 当设计项目结构时，系统应当采用清晰优雅简洁的目录架构
2. 当实现功能时，每个函数应当保持高内聚低耦合
3. 当组织代码时，项目嵌套层级不应过深
4. 当处理配置时，所有硬编码和配置项应当集中在config.py中
5. 当处理异常时，系统应当直接显示错误信息，避免过度的异常处理和业务降级逻辑

### 需求5：服务化部署

**用户故事：** 作为开发者，我希望工具能够作为服务运行，持续监听和处理查询请求。

#### 验收标准

1. 当预处理完成后，系统应当启动服务监听查询请求
2. 当接收到查询时，系统应当实时处理并返回结果
3. 当接受到刷新请求时，系统应当立即开始扫描代码库差异，刷新分片构建，分片洞察、关键字列表、embedding索引
4. 当服务运行时，系统应当保持稳定可靠的性能
5. 当评测时，系统应当能够通过`.venv/bin/python evaluation/end_to_end_evaluator.py`命令进行测试

### 需求6：多语言支持

**用户故事：** 作为开发者，我希望工具能够处理多种编程语言的代码文件。

#### 验收标准

1. 当处理代码时，系统应当支持Python语言的解析和分片
2. 当处理代码时，系统应当支持JavaScript和TypeScript语言的解析和分片
3. 当处理代码时，系统应当支持Java语言的解析和分片
4. 当处理代码时，系统应当支持C和C++语言的解析和分片
5. 当处理代码时，系统应当支持Rust语言的解析和分片
6. 当处理文档时，系统应当支持Markdown文件的解析和分片