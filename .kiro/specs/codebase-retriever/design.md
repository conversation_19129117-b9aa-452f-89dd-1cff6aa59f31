# 设计文档

## 概述

codebase_retriever是一个智能代码库检索工具，采用预处理+实时检索的架构模式。系统在启动时对代码库进行深度分析和索引构建，然后提供高精度的实时检索服务。整体设计遵循简洁优雅的原则，避免过度复杂的异常处理和业务降级逻辑。

## 架构

### 系统架构图

```mermaid
graph TB
    A[启动参数] --> B[代码库加载器]
    B --> C[Tree-sitter解析器]
    C --> D[代码分片器]
    D --> E[LLM洞察生成器]
    D --> F[嵌入向量计算器]
    D --> G[关键词提取器]
    E --> H[仓库洞察聚合器]
    F --> H
    G --> H
    H --> I[索引存储]
    I --> J[检索服务]
    
    K[用户查询] --> L[查询重写器]
    L --> M[Ripgrep执行器]
    M --> N[分片匹配器]
    N --> O[LLM筛选器]
    O --> P[结果排序器]
    P --> Q[响应输出]
    
    L --> R[嵌入检索器]
    R --> N
    
    J --> L
    I --> M
    I --> R
```

### 核心流程

1. **预处理阶段**：代码库加载 → Tree-sitter解析 → 分片处理 → 分片内容洞察生成 → 分片embedding索引构建
2. **检索阶段**：查询接收 → 意图识别 -> 实体/关键字检测 -> 查询重写 → Ripgrep搜索 → 分片匹配 → LLM筛选 → 结果输出
3. **降级检索**：Ripgrep无结果 → 嵌入检索 → 分片匹配 → LLM筛选 → 结果输出
4. **动态更新**：代码库文件更变 → 对应文件重新分片 -> 对应文件历史分片删除 -> 新分片的内容洞察生成 → 新分片embedding索引构建

## 组件和接口

### 1. 代码库处理模块 (codebase_processor)

#### CodebaseLoader
- **职责**：加载指定目录下的所有代码和文档文件
- **接口**：
  ```python
  def load_files(repo_path: str) -> List[FileInfo]
  def filter_supported_files(files: List[str]) -> List[str]
  ```

#### TreeSitterParser
- **职责**：使用tree-sitter解析不同语言的代码文件
- **接口**：
  ```python
  def parse_file(file_path: str, language: str) -> ParseTree
  def get_supported_languages() -> List[str]
  ```

#### CodeChunker
- **职责**：将解析后的代码按符号级粒度进行分片
- **接口**：
  ```python
  def chunk_by_symbols(parse_tree: ParseTree, file_info: FileInfo) -> List[CodeChunk]
  def chunk_markdown(content: str, file_info: FileInfo) -> List[CodeChunk]
  ```

### 2. 洞察生成模块 (insight_generator)

#### CodeInsightGenerator
- **职责**：为每个代码分片生成功能描述和洞察
- **接口**：
  ```python
  async def generate_insight(chunk: CodeChunk) -> str
  def batch_generate_insights(chunks: List[CodeChunk]) -> List[str]
  ```

#### KeywordExtractor
- **职责**：从代码分片中提取关键词（函数名、类名、变量名等）
- **接口**：
  ```python
  def extract_keywords(chunk: CodeChunk) -> List[str]
  def extract_file_path_keywords(file_path: str) -> List[str]
  ```

### 3. 向量化模块 (embedding_processor)

#### EmbeddingCalculator
- **职责**：计算代码分片的嵌入向量
- **接口**：
  ```python
  async def calculate_embedding(text: str) -> List[float]
  def batch_calculate_embeddings(texts: List[str]) -> List[List[float]]
  ```

### 4. 索引管理模块 (index_manager)

#### RepoIndex
- **职责**：管理整个仓库的索引数据
- **接口**：
  ```python
  def add_chunk(chunk: CodeChunk, insight: str, embedding: List[float], keywords: List[str])
  def get_repo_insight() -> str
  def save_index(file_path: str)
  def load_index(file_path: str)
  ```

### 5. 检索服务模块 (retrieval_service)

#### QueryRewriter
- **职责**：将用户查询重写为ripgrep命令
- **接口**：
  ```python
  def rewrite_to_ripgrep(query: str, repo_insight: str) -> List[str]
  def rewrite_to_embedding_query(query: str) -> str
  ```

#### RipgrepExecutor
- **职责**：执行ripgrep搜索命令
- **接口**：
  ```python
  def execute_search(commands: List[str]) -> List[SearchResult]
  def parse_ripgrep_output(output: str) -> List[SearchResult]
  ```

#### ChunkMatcher
- **职责**：将搜索结果匹配到具体的代码分片
- **接口**：
  ```python
  def match_chunks(search_results: List[SearchResult], index: RepoIndex) -> List[CodeChunk]
  ```

#### EmbeddingRetriever
- **职责**：基于嵌入向量进行相似性检索
- **接口**：
  ```python
  def retrieve_similar_chunks(query_embedding: List[float], index: RepoIndex, top_k: int) -> List[CodeChunk]
  ```

#### LLMFilter
- **职责**：使用LLM对检索到的分片进行相关性筛选
- **接口**：
  ```python
  async def filter_chunks(query: str, chunks: List[CodeChunk]) -> List[Tuple[CodeChunk, float]]
  ```

### 6. Web服务模块 (web_service)

#### RetrievalAPI
- **职责**：提供HTTP API接口
- **接口**：
  ```python
  @app.post("/query")
  async def query_endpoint(request: QueryRequest) -> QueryResponse
  
  @app.get("/health")
  async def health_check() -> HealthResponse
  ```

## 数据模型

### 核心数据结构

```python
@dataclass
class FileInfo:
    file_path: str
    relative_path: str
    language: str
    content: str
    size: int

@dataclass
class CodeChunk:
    id: str
    file_path: str
    start_line: int
    end_line: int
    content: str
    chunk_type: str  # function, class, method, etc.
    symbol_name: str
    language: str

@dataclass
class SearchResult:
    file_path: str
    line_number: int
    matched_text: str
    context: str

@dataclass
class QueryRequest:
    query: str
    top_k: int = 10

@dataclass
class QueryResponse:
    results: List[RetrievalResult]
    total_time_ms: float

@dataclass
class RetrievalResult:
    file_path: str
    text: str
    score: float
    chunk_type: str
    symbol_name: str
```

## 错误处理

系统采用"快速失败"的错误处理策略，避免复杂的降级逻辑：

1. **文件加载错误**：直接跳过无法读取的文件，记录错误日志
2. **解析错误**：跳过无法解析的文件，继续处理其他文件
3. **LLM调用失败**：抛出异常，停止处理，要求用户检查配置
4. **嵌入计算失败**：抛出异常，停止处理
5. **网络请求超时**：直接返回错误响应，不进行重试

## 测试策略

### 单元测试
- 每个模块的核心功能都需要单元测试
- 重点测试代码分片、关键词提取、查询重写等核心逻辑
- 使用mock对象模拟LLM和嵌入服务调用

### 集成测试
- 端到端的检索流程测试
- 使用小型测试代码库验证完整流程
- 测试不同编程语言的支持情况
- 使用evaluation/end_to_end_evaluator.py进行评测
- 目标指标：P@1 > 80%, MRR@K > 70%
- 监控查询响应时间和内存使用

## 配置管理

所有配置项都集中在config.py中管理：

```python
# 支持的编程语言
SUPPORTED_LANGUAGES = {
    'python': '.py',
    'javascript': '.js',
    'typescript': '.ts',
    'java': '.java',
    'c': '.c',
    'cpp': ['.cpp', '.cc', '.cxx'],
    'rust': '.rs',
    'markdown': '.md'
}

# Tree-sitter配置
TREE_SITTER_CONFIG = {
    'chunk_min_lines': 5,
    'chunk_max_lines': 100,
    'symbol_types': ['function', 'class', 'method', 'interface']
}

# 检索配置
RETRIEVAL_CONFIG = {
    'ripgrep_timeout': 10,
    'max_ripgrep_results': 100,
    'embedding_top_k': 50,
    'llm_filter_threshold': 0.7
}

# 服务配置
SERVICE_CONFIG = {
    'host': '127.0.0.1',
    'port': 5001,
    'workers': 1
}
```

## 部署架构

### 目录结构
```
codebase_retriever/
├── config.py                 # 配置文件
├── main.py                   # 主入口
├── codebase_processor/       # 代码库处理模块
│   ├── __init__.py
│   ├── loader.py
│   ├── parser.py
│   └── chunker.py
├── insight_generator/        # 洞察生成模块
│   ├── __init__.py
│   ├── code_insight.py
│   └── keyword_extractor.py
├── embedding_processor/      # 向量化模块
│   ├── __init__.py
│   └── calculator.py
├── index_manager/           # 索引管理模块
│   ├── __init__.py
│   └── repo_index.py
├── retrieval_service/       # 检索服务模块
│   ├── __init__.py
│   ├── query_rewriter.py
│   ├── ripgrep_executor.py
│   ├── chunk_matcher.py
│   ├── embedding_retriever.py
│   └── llm_filter.py
├── web_service/             # Web服务模块
│   ├── __init__.py
│   └── api.py
└── utils/                   # 工具模块
    ├── __init__.py
    └── helpers.py
```

### 启动流程
1. 解析命令行参数（repo-path, port等）
2. 加载配置文件
3. 加载持久化索引（如果有的话）
    4. 对比文件crc32，识别文件增删改，更新持久化索引
3. 初始化各个模块
    4. 执行代码库预处理
    5. 构建索引
6. 保存持久化索引
7. 启动Web服务
8. 监听查询请求

### 性能优化
- 使用异步I/O处理LLM和嵌入调用
- 并发批量处理代码分片以提高效率
- 内存中缓存索引数据
- 使用连接池管理HTTP请求