# 实施计划

- [x] 1. 建立项目基础架构和配置
  - 创建清晰的目录结构，包含所有核心模块
  - 扩展config.py配置文件，添加codebase_retriever相关配置项
  - 创建基础的数据模型类和接口定义
  - _需求: 4.1, 4.2, 4.4_

- [x] 2. 实现代码库处理核心功能
  - [x] 2.1 实现CodebaseLoader文件加载器
    - 编写文件遍历和过滤逻辑，支持指定的编程语言扩展名
    - 实现文件内容读取和FileInfo对象创建
    - 添加基础的文件类型检测和语言识别功能
    - _需求: 1.1, 6.1-6.6_

  - [x] 2.2 集成Tree-sitter解析器
    - 安装和配置tree-sitter库及各语言的语法文件
    - 实现TreeSitterParser类，支持Python/JS/TS/Java/C/C++/Rust解析
    - 编写解析树遍历和符号提取逻辑
    - _需求: 1.2, 6.1-6.6_

  - [x] 2.3 实现代码分片器
    - 编写CodeChunker类，按符号级粒度分片代码
    - 实现函数、类、方法等不同符号类型的分片逻辑
    - 添加Markdown文件的分片处理支持
    - 创建CodeChunk数据结构和相关方法
    - _需求: 1.2, 6.6_

- [x] 3. 实现洞察生成和关键词提取
  - [x] 3.1 实现代码洞察生成器
    - 编写CodeInsightGenerator类，调用LLM生成代码分片的功能描述
    - 实现批量处理逻辑，提高LLM调用效率
    - 集成config.py中的LLM配置和异步调用机制
    - _需求: 1.3_

  - [x] 3.2 实现关键词提取器
    - 编写KeywordExtractor类，从代码分片中提取函数名、类名、变量名
    - 实现文件路径关键词提取功能
    - 添加不同编程语言的关键词提取规则
    - _需求: 1.5_

- [x] 4. 实现嵌入向量计算
  - [x] 4.1 实现嵌入计算器
    - 编写EmbeddingCalculator类，调用嵌入模型计算向量
    - 实现批量嵌入计算功能，优化API调用效率
    - 集成config.py中的嵌入模型配置
    - _需求: 1.4_

- [x] 5. 实现索引管理系统
  - [x] 5.1 实现仓库索引管理器
    - 编写RepoIndex类，管理所有代码分片的索引数据
    - 实现索引的内存存储和文件持久化功能（JSON/pickle格式）
    - 创建repo_insight聚合逻辑，生成整体仓库洞察
    - _需求: 1.5_

  - [x] 5.2 实现增量更新机制
    - 实现文件CRC32校验和变更检测功能
    - 编写文件增删改的差异识别逻辑
    - 实现历史分片删除和新分片添加的索引更新机制
    - 添加增量更新的洞察生成、关键字列表更新和嵌入计算
    - _需求: 1.1-1.5_

- [x] 6. 实现查询重写和Ripgrep执行
  - [x] 6.1 实现查询重写器
    - 编写QueryRewriter类，将自然语言查询转换为ripgrep命令
    - 实现基于repo_insight的智能查询重写逻辑
    - 添加嵌入检索查询的重写功能
    - _需求: 2.1, 2.4_

  - [x] 6.2 实现Ripgrep执行器
    - 编写RipgrepExecutor类，执行ripgrep搜索命令
    - 实现ripgrep输出解析和SearchResult对象创建
    - 添加搜索超时和错误处理机制
    - _需求: 2.2_

- [x] 7. 实现分片匹配和检索功能
  - [x] 7.1 实现分片匹配器
    - 编写ChunkMatcher类，将搜索结果匹配到具体代码分片
    - 实现基于行号和文件路径的精确匹配逻辑
    - _需求: 2.2_

  - [x] 7.2 实现嵌入检索器
    - 编写EmbeddingRetriever类，基于向量相似性进行检索
    - 实现余弦相似度计算和top-k结果筛选
    - _需求: 2.4, 2.5_

- [x] 8. 实现LLM筛选和结果排序
  - [x] 8.1 实现LLM筛选器
    - 编写LLMFilter类，使用LLM评估分片与查询的相关性
    - 实现批量筛选逻辑，提高筛选效率
    - 添加相关性评分机制
    - _需求: 2.3, 2.5_

  - [x] 8.2 实现结果排序和输出
    - 编写结果排序逻辑，按相关性分数排序
    - 实现RetrievalResult数据结构和格式化输出
    - _需求: 3.1, 3.2, 3.3_

- [x] 9. 实现Web服务API
  - [x] 9.1 实现HTTP API接口
    - 编写RetrievalAPI类，提供/query和/health端点
    - 实现请求参数验证和响应格式化
    - 添加CORS支持和基础的错误处理
    - _需求: 5.1, 5.2_

  - [x] 9.2 集成完整检索流程
    - 将所有模块集成到API端点中
    - 实现Ripgrep优先，嵌入检索降级的完整流程
    - 添加查询时间统计和性能监控
    - _需求: 2.1-2.5, 5.3_

- [x] 10. 实现主程序和启动逻辑
  - [x] 10.1 编写主程序入口和启动流程
    - 实现main.py，解析命令行参数（repo-path, port等）
    - 实现配置文件加载和模块初始化逻辑
    - 添加持久化索引加载功能，如果索引文件存在则加载
    - 实现文件变更检测：对比文件CRC32，识别增删改文件
    - 添加增量更新流程：删除历史分片→重新处理变更文件→更新索引
    - 实现完整预处理流程：加载→解析→分片→洞察→嵌入→索引构建
    - 添加索引持久化保存功能
    - 实现Web服务启动和优雅关闭逻辑
    - _需求: 1.1-1.6, 5.1_

- [x] 11. 编写测试和验证
  - [x] 11.1 编写单元测试
    - 为核心模块编写单元测试，重点测试分片、关键词提取、查询重写
    - 使用mock对象模拟LLM和嵌入服务调用
    - 确保测试覆盖率达到合理水平
    - _需求: 3.1, 3.2_

  - [x] 11.2 集成评测系统
    - 确保系统能够通过evaluation/end_to_end_evaluator.py评测
    - 验证API接口格式与评测器的兼容性
    - 优化系统性能，达到P@1>80%, MRR@K>70%的目标指标
    - _需求: 3.1, 3.2, 3.3, 5.4_