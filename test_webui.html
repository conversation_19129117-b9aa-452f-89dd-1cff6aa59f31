<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodebaseQA 测试界面</title>
    <style>
        :root {
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #374151;
            --text-secondary: #6b7280;
            --text-accent: #6366f1;
            --border-color: #e5e7eb;
            --border-focus: #667eea;
            --code-bg: #ffffff;
            --hover-shadow: rgba(102, 126, 234, 0.3);
        }

        [data-theme="dark"] {
            --bg-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            --bg-primary: #1e293b;
            --bg-secondary: #334155;
            --bg-tertiary: #475569;
            --text-primary: #f1f5f9;
            --text-secondary: #cbd5e1;
            --text-accent: #818cf8;
            --border-color: #475569;
            --border-focus: #818cf8;
            --code-bg: #0f172a;
            --hover-shadow: rgba(129, 140, 248, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: var(--bg-gradient);
            min-height: 100vh;
            padding: 20px;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--bg-primary);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 0.95rem;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .query-section {
            background: var(--bg-secondary);
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .query-section h3 {
            color: var(--text-primary);
            margin-bottom: 20px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 13px;
            transition: border-color 0.3s ease;
            resize: vertical;
            min-height: 120px;
            font-family: inherit;
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .form-group textarea:focus {
            outline: none;
            border-color: var(--border-focus);
            box-shadow: 0 0 0 3px var(--hover-shadow);
        }

        .form-group select {
            width: 200px;
            padding: 10px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 13px;
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: border-color 0.3s ease;
        }

        .form-group select:focus {
            outline: none;
            border-color: var(--border-focus);
        }

        .form-group small {
            color: var(--text-secondary);
            font-size: 11px;
            margin-top: 5px;
            display: block;
            line-height: 1.4;
        }



        .help-text {
            color: var(--text-secondary);
            font-size: 11px;
            font-style: italic;
            margin-top: 4px;
        }

        .button-group {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px var(--hover-shadow);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: #6b7280;
        }

        .btn-secondary:hover {
            background: #4b5563;
            box-shadow: 0 8px 20px rgba(107, 114, 128, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            margin-bottom: 25px;
            font-size: 12px;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #ef4444;
        }

        .status-dot.online {
            background: #10b981;
        }

        .response-container {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 25px;
            margin-top: 25px;
            transition: all 0.3s ease;
        }

        .response-container h3 {
            color: var(--text-primary);
            margin-bottom: 20px;
            font-size: 1rem;
        }

        .result-item {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            transition: box-shadow 0.3s ease;
        }

        .result-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .result-header {
            margin-bottom: 12px;
        }

        .result-meta {
            margin-top: 6px;
        }

        .file-path {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            color: var(--text-accent);
            background: var(--bg-tertiary);
            padding: 4px 8px;
            border-radius: 4px;
        }

        .score {
            font-size: 11px;
            color: var(--text-secondary);
            background: var(--bg-tertiary);
            padding: 2px 8px;
            border-radius: 4px;
        }

        .result-content {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 11px;
            line-height: 1.4;
            background: var(--code-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 15px;
            white-space: pre-wrap;
            overflow-x: auto;
            margin-top: 8px;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .line-range {
            display: inline-block;
            font-size: 10px;
            color: var(--text-accent);
            background: var(--bg-tertiary);
            padding: 2px 6px;
            border-radius: 3px;
            margin-right: 8px;
            font-weight: 500;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        .category-badge {
            display: inline-block;
            font-size: 10px;
            color: var(--text-primary);
            background: var(--bg-tertiary);
            padding: 2px 6px;
            border-radius: 3px;
            margin-right: 8px;
            font-weight: 500;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .error {
            color: #ef4444;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .query-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--bg-secondary);
            border-radius: 8px;
            border-left: 4px solid var(--text-accent);
        }

        .query-text {
            font-weight: 600;
            color: var(--text-primary);
        }

        .query-stats {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: var(--bg-primary);
            border: 2px solid var(--border-color);
            border-radius: 50px;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px var(--hover-shadow);
        }
    </style>
</head>

<body>
    <button class="theme-toggle" onclick="toggleTheme()" id="theme-toggle">🌙 暗黑模式</button>
    <div class="container">
        <div class="header">
            <h1>🚀 CodebaseQA</h1>
            <p>智能代码检索测试界面</p>
        </div>

        <div class="content">
            <!-- 服务状态栏 -->
            <div class="status-bar">
                <div class="status-item">
                    <div class="status-dot" id="status-dot"></div>
                    <span id="status-text">检查中...</span>
                </div>
                <div class="status-item">
                    <span>服务地址: <strong id="current-url">http://localhost:5001</strong></span>
                </div>
                <div class="status-item">
                    <span>响应时间: <strong id="response-time">-</strong></span>
                </div>
            </div>

            <!-- 查询区域 -->
            <div class="query-section">
                <h3>🔍 代码检索</h3>

                <div class="form-group">
                    <label for="query-text">查询内容:</label>
                    <textarea id="query-text"
                        placeholder="请输入您想要搜索的代码相关问题...&#10;&#10;例如:&#10;- 如何启动推理服务？&#10;- 处理文件的函数有哪些？&#10;- 错误处理的实现方式&#10;- 配置文件的读取逻辑&#10;- 用户认证相关功能&#10;- 数据库连接的实现"></textarea>
                </div>

                <div class="form-group">
                    <label for="top-k">返回结果数量:</label>
                    <select id="top-k">
                        <option value="5">5个结果</option>
                        <option value="10" selected>10个结果</option>
                    </select>
                </div>



                <div class="button-group">
                    <button class="btn" onclick="performQuery()" id="query-btn">
                        <span id="query-btn-text">开始检索</span>
                    </button>
                    <button class="btn btn-secondary" onclick="clearResults()">清空结果</button>
                    <div class="form-group" style="margin: 0;">
                        <input type="text" id="base-url" value="http://localhost:5001" placeholder="服务地址"
                            style="width: 200px; padding: 8px 12px; border: 2px solid var(--border-color); border-radius: 6px; font-size: 13px; background: var(--bg-primary); color: var(--text-primary);">
                    </div>
                </div>
            </div>

            <!-- 响应显示区域 -->
            <div class="response-container" id="response-container" style="display: none;">
                <div id="query-info" class="query-info"></div>
                <h3>检索结果</h3>
                <div id="results-content"></div>
            </div>
        </div>
    </div>

    <script>
        let baseUrl = 'http://localhost:5001';

        // 主题切换功能
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const toggleBtn = document.getElementById('theme-toggle');
            if (newTheme === 'dark') {
                toggleBtn.innerHTML = '☀️ 浅色模式';
            } else {
                toggleBtn.innerHTML = '🌙 暗黑模式';
            }
        }

        // 初始化主题
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const toggleBtn = document.getElementById('theme-toggle');
            if (savedTheme === 'dark') {
                toggleBtn.innerHTML = '☀️ 浅色模式';
            } else {
                toggleBtn.innerHTML = '🌙 暗黑模式';
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function () {
            initTheme();

            baseUrl = document.getElementById('base-url').value;
            document.getElementById('current-url').textContent = baseUrl;
            checkServiceStatus();

            // 监听服务地址变化
            document.getElementById('base-url').addEventListener('change', function () {
                baseUrl = this.value;
                document.getElementById('current-url').textContent = baseUrl;
                checkServiceStatus();
            });
        });

        // 检查服务状态
        async function checkServiceStatus() {
            const statusDot = document.getElementById('status-dot');
            const statusText = document.getElementById('status-text');
            const responseTimeEl = document.getElementById('response-time');

            try {
                const startTime = Date.now();
                const response = await fetch(`${baseUrl}/health`);
                const responseTime = Date.now() - startTime;

                if (response.ok) {
                    statusDot.classList.add('online');
                    statusText.textContent = '服务在线';
                    responseTimeEl.textContent = `${responseTime}ms`;
                } else {
                    throw new Error('服务响应异常');
                }
            } catch (error) {
                statusDot.classList.remove('online');
                statusText.textContent = '服务离线';
                responseTimeEl.textContent = '-';
            }
        }

        // 执行查询
        async function performQuery() {
            const queryText = document.getElementById('query-text').value.trim();
            if (!queryText) {
                alert('请输入查询内容');
                return;
            }

            const topK = parseInt(document.getElementById('top-k').value);

            const queryBtn = document.getElementById('query-btn');
            const btnText = document.getElementById('query-btn-text');

            // 设置加载状态
            queryBtn.disabled = true;
            btnText.innerHTML = '<span class="loading"></span> 检索中...';

            try {
                const startTime = Date.now();

                // 构建请求体
                const requestBody = {
                    query: queryText,
                    top_k: topK
                };

                const response = await fetch(`${baseUrl}/query`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                const responseTime = Date.now() - startTime;

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResults(data, responseTime);

            } catch (error) {
                displayError(`查询失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                queryBtn.disabled = false;
                btnText.textContent = '开始检索';
            }
        }



        // 显示查询结果
        function displayResults(data, responseTime) {
            const container = document.getElementById('response-container');
            const queryInfo = document.getElementById('query-info');
            const resultsContent = document.getElementById('results-content');

            // 显示查询信息
            queryInfo.innerHTML = `
                <div class="query-text">查询: "${data.query}"</div>
                <div class="query-stats">找到 ${data.results.length} 个结果 • 耗时 ${responseTime}ms</div>
            `;

            // 显示结果
            if (data.results && data.results.length > 0) {
                resultsContent.innerHTML = data.results.map((result, index) => {
                    const metadata = result.metadata || {};
                    const startLine = metadata.start_line || 1;
                    const endLine = metadata.end_line || startLine;
                    const category = metadata.category || 'unknown';
                    const score = result.score || 0;
                    const lineRange = startLine === endLine ? `${startLine}` : `${startLine}-${endLine}`;

                    return `
                        <div class="result-item">
                            <div class="result-header">
                                <div class="file-path">${result.file_path || '未知文件'}</div>
                                <div class="result-meta">
                                    <span class="line-range">${lineRange}</span>
                                    <span class="category-badge">${category}</span>
                                    <span class="score">相关度: ${score.toFixed(3)}</span>
                                </div>
                            </div>
                            <div class="result-content">${result.text || '无内容'}</div>
                        </div>
                    `;
                }).join('');
            } else {
                resultsContent.innerHTML = '<div class="error">未找到相关结果</div>';
            }

            container.style.display = 'block';
            container.scrollIntoView({ behavior: 'smooth' });
        }

        // 显示错误信息
        function displayError(message) {
            const container = document.getElementById('response-container');
            const queryInfo = document.getElementById('query-info');
            const resultsContent = document.getElementById('results-content');

            queryInfo.innerHTML = '';
            resultsContent.innerHTML = `<div class="error">${message}</div>`;

            container.style.display = 'block';
            container.scrollIntoView({ behavior: 'smooth' });
        }



        // 清空结果
        function clearResults() {
            const container = document.getElementById('response-container');
            container.style.display = 'none';
            document.getElementById('query-text').value = '';
        }

        // 回车键提交
        document.getElementById('query-text').addEventListener('keydown', function (e) {
            if (e.ctrlKey && e.key === 'Enter') {
                performQuery();
            }
        });
    </script>
</body>

</html>