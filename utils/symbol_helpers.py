"""
符号处理相关的工具函数
"""

import hashlib
import re
from typing import Optional, Dict, Any
from .models import Symbol, SymbolType, Parameter


def generate_symbol_id(
    symbol_name: str,
    file_path: str,
    start_line: int,
    symbol_type: SymbolType,
    namespace: str = None,
    parent_symbol_name: str = None,
) -> str:
    """生成符号的唯一标识符，包含层级结构信息"""
    # 构建完整的符号路径，包含层级信息
    path_parts = []

    # 添加命名空间
    if namespace:
        path_parts.append(namespace)

    # 添加父符号名称（如类名）
    if parent_symbol_name:
        path_parts.append(parent_symbol_name)

    # 添加符号名称
    path_parts.append(symbol_name)

    # 构建完整路径
    full_path = ".".join(path_parts)

    # 使用完整路径、文件路径、行号和类型生成唯一ID
    content = f"{full_path}:{file_path}:{start_line}:{symbol_type.value}"
    return hashlib.md5(content.encode("utf-8")).hexdigest()


def normalize_symbol_name(name: str) -> str:
    """标准化符号名称"""
    # 移除多余的空格和特殊字符
    name = re.sub(r"\s+", " ", name.strip())
    # 移除可能的引号
    name = name.strip("\"'")
    return name


def extract_namespace_from_path(file_path: str, language: str) -> Optional[str]:
    """从文件路径提取命名空间"""
    if language == "java":
        # Java包名通常对应目录结构
        parts = file_path.replace("\\", "/").split("/")
        # 移除文件名和src/main/java等常见前缀
        filtered_parts = []
        skip_next = False
        for part in parts[:-1]:  # 排除文件名
            if skip_next:
                skip_next = False
                continue
            if part in ["src", "main", "java", "test"]:
                if part in ["src", "main"]:
                    skip_next = True
                continue
            filtered_parts.append(part)
        return ".".join(filtered_parts) if filtered_parts else None

    elif language in ["javascript", "typescript"]:
        # JS/TS模块路径
        parts = file_path.replace("\\", "/").split("/")
        # 移除文件扩展名
        if parts:
            parts[-1] = parts[-1].rsplit(".", 1)[0]
        return "/".join(parts)

    elif language == "python":
        # Python模块路径
        parts = file_path.replace("\\", "/").split("/")
        # 移除.py扩展名
        if parts:
            parts[-1] = parts[-1].replace(".py", "")
        return ".".join(parts)

    return None


def parse_function_signature(signature: str, language: str) -> Dict[str, Any]:
    """解析函数签名，提取参数和返回值信息"""
    result = {"parameters": [], "return_type": None, "modifiers": []}

    if language == "python":
        return _parse_python_signature(signature, result)
    elif language in ["javascript", "typescript"]:
        return _parse_js_signature(signature, result)
    elif language == "java":
        return _parse_java_signature(signature, result)

    return result


def _parse_python_signature(signature: str, result: Dict[str, Any]) -> Dict[str, Any]:
    """解析Python函数签名"""
    # 简单的Python签名解析
    # 格式: def function_name(param1: type1, param2: type2 = default) -> return_type:

    # 提取参数部分
    if "(" in signature and ")" in signature:
        params_part = signature[signature.find("(") + 1 : signature.rfind(")")]
        if params_part.strip():
            params = [p.strip() for p in params_part.split(",")]
            for param in params:
                param_info = _parse_python_parameter(param)
                if param_info:
                    result["parameters"].append(param_info)

    # 提取返回值类型
    if "->" in signature:
        return_part = signature.split("->")[-1].strip().rstrip(":")
        result["return_type"] = return_part

    # 提取修饰符
    if signature.strip().startswith("async def"):
        result["modifiers"].append("async")

    return result


def _parse_python_parameter(param: str) -> Optional[Parameter]:
    """解析Python参数"""
    param = param.strip()
    if not param or param in ["self", "cls"]:
        return None

    # 处理可变参数
    is_variadic = param.startswith("*")
    if is_variadic:
        param = param.lstrip("*")

    # 分离名称、类型和默认值
    name = param
    param_type = None
    default_value = None
    is_optional = False

    # 处理默认值
    if "=" in param:
        name, default_value = param.split("=", 1)
        name = name.strip()
        default_value = default_value.strip()
        is_optional = True

    # 处理类型注解
    if ":" in name:
        name, param_type = name.split(":", 1)
        name = name.strip()
        param_type = param_type.strip()

    return Parameter(
        name=name,
        param_type=param_type,
        default_value=default_value,
        is_optional=is_optional,
        is_variadic=is_variadic,
    )


def _parse_js_signature(signature: str, result: Dict[str, Any]) -> Dict[str, Any]:
    """解析JavaScript/TypeScript函数签名"""
    # 简化的JS/TS签名解析
    # 支持: function name(params): returnType, (params) => returnType, name(params): returnType

    # 提取参数部分
    if "(" in signature and ")" in signature:
        params_part = signature[signature.find("(") + 1 : signature.rfind(")")]
        if params_part.strip():
            params = [p.strip() for p in params_part.split(",")]
            for param in params:
                param_info = _parse_js_parameter(param)
                if param_info:
                    result["parameters"].append(param_info)

    # 提取返回值类型（TypeScript）
    if ":" in signature and "=>" not in signature:
        # 函数声明形式
        parts = signature.split(":")
        if len(parts) > 1:
            return_part = parts[-1].strip().rstrip("{")
            result["return_type"] = return_part
    elif "=>" in signature:
        # 箭头函数形式
        return_part = signature.split("=>")[-1].strip().rstrip("{")
        result["return_type"] = return_part

    # 提取修饰符
    if "async" in signature:
        result["modifiers"].append("async")
    if "static" in signature:
        result["modifiers"].append("static")

    return result


def _parse_js_parameter(param: str) -> Optional[Parameter]:
    """解析JavaScript/TypeScript参数"""
    param = param.strip()
    if not param:
        return None

    # 处理可变参数
    is_variadic = param.startswith("...")
    if is_variadic:
        param = param[3:]

    name = param
    param_type = None
    default_value = None
    is_optional = "?" in param

    # 处理可选参数
    if "?" in param:
        param = param.replace("?", "")

    # 处理默认值
    if "=" in param:
        name, default_value = param.split("=", 1)
        name = name.strip()
        default_value = default_value.strip()
        is_optional = True

    # 处理类型注解
    if ":" in name:
        name, param_type = name.split(":", 1)
        name = name.strip()
        param_type = param_type.strip()

    return Parameter(
        name=name,
        param_type=param_type,
        default_value=default_value,
        is_optional=is_optional,
        is_variadic=is_variadic,
    )


def _parse_java_signature(signature: str, result: Dict[str, Any]) -> Dict[str, Any]:
    """解析Java方法签名"""
    # 简化的Java签名解析
    # 格式: [modifiers] returnType methodName(parameters) [throws exceptions]

    # 提取修饰符
    modifiers = []
    for modifier in [
        "public",
        "private",
        "protected",
        "static",
        "final",
        "abstract",
        "synchronized",
    ]:
        if modifier in signature:
            modifiers.append(modifier)
    result["modifiers"] = modifiers

    # 提取参数部分
    if "(" in signature and ")" in signature:
        params_part = signature[signature.find("(") + 1 : signature.rfind(")")]
        if params_part.strip():
            params = [p.strip() for p in params_part.split(",")]
            for param in params:
                param_info = _parse_java_parameter(param)
                if param_info:
                    result["parameters"].append(param_info)

    # 提取返回值类型（简化处理）
    # 这里需要更复杂的解析逻辑来准确提取返回值类型

    return result


def _parse_java_parameter(param: str) -> Optional[Parameter]:
    """解析Java参数"""
    param = param.strip()
    if not param:
        return None

    # Java参数格式: [final] Type name
    parts = param.split()
    if len(parts) < 2:
        return None

    # 处理可变参数
    is_variadic = "..." in param

    # 最后一个是参数名，倒数第二个是类型
    name = parts[-1]
    param_type = parts[-2]

    if is_variadic:
        param_type = param_type.replace("...", "[]")

    return Parameter(name=name, param_type=param_type, is_variadic=is_variadic)


def calculate_symbol_similarity(symbol1: Symbol, symbol2: Symbol) -> float:
    """计算两个符号的相似度"""
    if symbol1.name == symbol2.name:
        return 1.0

    # 基于编辑距离的相似度计算
    distance = _levenshtein_distance(symbol1.name.lower(), symbol2.name.lower())
    max_len = max(len(symbol1.name), len(symbol2.name))

    if max_len == 0:
        return 1.0

    similarity = 1.0 - (distance / max_len)

    # 如果符号类型相同，增加相似度
    if symbol1.symbol_type == symbol2.symbol_type:
        similarity += 0.1

    # 如果在同一文件中，增加相似度
    if symbol1.file_path == symbol2.file_path:
        similarity += 0.1

    return min(similarity, 1.0)


def _levenshtein_distance(s1: str, s2: str) -> int:
    """计算两个字符串的编辑距离"""
    if len(s1) < len(s2):
        return _levenshtein_distance(s2, s1)

    if len(s2) == 0:
        return len(s1)

    previous_row = list(range(len(s2) + 1))
    for i, c1 in enumerate(s1):
        current_row = [i + 1]
        for j, c2 in enumerate(s2):
            insertions = previous_row[j + 1] + 1
            deletions = current_row[j] + 1
            substitutions = previous_row[j] + (c1 != c2)
            current_row.append(min(insertions, deletions, substitutions))
        previous_row = current_row

    return previous_row[-1]


def format_symbol_signature(symbol: Symbol) -> str:
    """格式化符号签名用于显示"""
    if symbol.signature:
        return symbol.signature

    # 根据符号类型生成基本签名
    if symbol.symbol_type == SymbolType.FUNCTION:
        params = ", ".join([p.name for p in symbol.parameters])
        return f"{symbol.name}({params})"
    elif symbol.symbol_type == SymbolType.CLASS:
        return f"class {symbol.name}"
    elif symbol.symbol_type == SymbolType.METHOD:
        params = ", ".join([p.name for p in symbol.parameters])
        return f"{symbol.name}({params})"
    else:
        return symbol.name


def get_symbol_context_path(symbol: Symbol) -> str:
    """获取符号的完整上下文路径"""
    parts = []

    if symbol.namespace:
        parts.append(symbol.namespace)

    parts.append(symbol.name)

    return ".".join(parts)


def get_symbol_qualified_name(symbol: Symbol) -> str:
    """获取符号的完全限定名称，包含所有层级信息"""
    parts = []

    # 添加模块路径（如果有）
    if symbol.module_path:
        # 清理模块路径，移除文件扩展名
        module_parts = symbol.module_path.replace("\\", "/").split("/")
        if module_parts:
            # 移除文件扩展名
            last_part = module_parts[-1]
            if "." in last_part:
                module_parts[-1] = last_part.rsplit(".", 1)[0]
            parts.extend(module_parts)

    # 添加命名空间
    if symbol.namespace:
        parts.append(symbol.namespace)

    # 添加符号名称
    parts.append(symbol.name)

    return ".".join(parts)


def extract_symbol_hierarchy_info(symbol: Symbol) -> dict:
    """提取符号的层级结构信息，用于检索和匹配"""
    return {
        "qualified_name": get_symbol_qualified_name(symbol),
        "context_path": get_symbol_context_path(symbol),
        "namespace": symbol.namespace or "",
        "module_path": symbol.module_path or "",
        "parent_symbol": symbol.parent_symbol or "",
        "hierarchy_depth": len(
            [p for p in [symbol.namespace, symbol.parent_symbol] if p]
        ),
    }
