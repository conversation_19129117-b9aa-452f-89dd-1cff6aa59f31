"""
通用辅助工具
提供系统中常用的工具函数
"""

import os
import hashlib
import zlib
from pathlib import Path
from typing import List, Optional
from config import SUPPORTED_LANGUAGES


def get_file_language(file_path: str) -> Optional[str]:
    """根据文件扩展名确定编程语言"""
    file_ext = Path(file_path).suffix.lower()

    for language, extensions in SUPPORTED_LANGUAGES.items():
        if file_ext in extensions:
            return language

    return None


def calculate_file_crc32(file_path: str) -> str:
    """计算文件内容的CRC32校验和"""
    try:
        with open(file_path, "rb") as f:
            content = f.read()
            return format(zlib.crc32(content) & 0xFFFFFFFF, "08x")
    except Exception:
        return ""


def is_text_file(file_path: str) -> bool:
    """判断是否为文本文件"""
    try:
        with open(file_path, "rb") as f:
            chunk = f.read(1024)
            if b"\0" in chunk:
                return False
            return True
    except Exception:
        return False


def normalize_path(path: str) -> str:
    """标准化文件路径"""
    return os.path.normpath(path).replace("\\", "/")


def get_relative_path(file_path: str, repo_path: str) -> str:
    """获取相对于仓库根目录的路径"""
    try:
        return os.path.relpath(file_path, repo_path).replace("\\", "/")
    except ValueError:
        return os.path.basename(file_path)


def truncate_text(text: str, max_length: int) -> str:
    """截断文本到指定长度"""
    if len(text) <= max_length:
        return text
    return text[: max_length - 3] + "..."


def clean_code_content(content: str) -> str:
    """清理代码内容，移除多余的空白字符"""
    lines = content.split("\n")
    # 移除空行
    non_empty_lines = [line.rstrip() for line in lines if line.strip()]
    return "\n".join(non_empty_lines)


def extract_function_name(symbol_name: str) -> str:
    """从符号名称中提取函数名"""
    # 处理类似 "ClassName.methodName" 的情况
    if "." in symbol_name:
        return symbol_name.split(".")[-1]
    return symbol_name


def generate_chunk_id(
    file_path: str, start_line: int, end_line: int, symbol_name: str
) -> str:
    """生成代码分片的唯一ID"""
    content = f"{file_path}:{start_line}-{end_line}:{symbol_name}"
    return hashlib.md5(content.encode()).hexdigest()


def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
    """计算两个向量的余弦相似度"""
    if len(vec1) != len(vec2):
        return 0.0

    dot_product = sum(a * b for a, b in zip(vec1, vec2))
    magnitude1 = sum(a * a for a in vec1) ** 0.5
    magnitude2 = sum(b * b for b in vec2) ** 0.5

    if magnitude1 == 0 or magnitude2 == 0:
        return 0.0

    return dot_product / (magnitude1 * magnitude2)


def safe_filename(filename: str) -> str:
    """生成安全的文件名"""
    # 移除或替换不安全的字符
    unsafe_chars = '<>:"/\\|?*'
    for char in unsafe_chars:
        filename = filename.replace(char, "_")
    return filename


def ensure_directory(dir_path: str):
    """确保目录存在，如果不存在则创建"""
    Path(dir_path).mkdir(parents=True, exist_ok=True)
