# Codebase Retriever

智能代码库检索工具，用于在大型代码仓中高精度检索相关代码片段。基于“代码分片 + 嵌入向量 + LLM”的混合方案，支持增量索引与REST API服务。

## 功能特性

- **混合检索**: 设计支持 ripgrep + 向量召回 的混合流程；当前默认以向量检索为主，必要时可接入文本检索补强
- **LLM 增强**: 生成分片洞察（代码意图描述）、相关性筛选、多结果多样化处理
- **多语言解析**: Tree-sitter 与降级文本解析并行，支持 Python/JavaScript/TypeScript/Java/C/C++/Rust/ArkTS/Markdown
- **ArkTS 支持**: 集成 ArkTS 专用解析器，缺库或不支持架构时自动降级
- **增量更新**: 识别新增/修改/删除文件，仅处理差异，支持定时扫描
- **高性能持久化**: 分片与元数据分文件持久化，可压缩存储，大仓库冷启动更友好
- **Web API**: FastAPI + Uvicorn 提供可观测的服务端点

## 安装与启动

### 依赖安装

```bash
# 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # macOS/Linux
# 或 .venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 安装 ripgrep（可选，若缺失会降级到 grep）
# Ubuntu/Debian: sudo apt install ripgrep
# macOS: brew install ripgrep
# Windows: choco install ripgrep
```

### 启动服务（CLI）

```bash
# 最简启动（指定仓库路径）
python main.py --repo-path /path/to/repo

# 指定端口与日志级别
python main.py --repo-path /path/to/repo --port 5001 --log-level DEBUG

# 清空缓存后重建索引
python main.py --repo-path /path/to/repo --clear-cache

# 关闭增量更新（每次全量重建）
python main.py --repo-path /path/to/repo --no-incremental

# 关闭定时扫描、调整扫描间隔（秒）
python main.py --repo-path /path/to/repo --no-scheduler --scan-interval 300

# 指定索引缓存根目录
python main.py --repo-path /path/to/repo --index-dir .codebase_index

# 缓存管理
python main.py --list-cache
python main.py --clean-all-cache
```

说明：每个仓库会生成独立缓存目录，目录名形如 `repoName_<hash>`，索引数据位于该目录下的 `repoName/` 子目录中。

## REST API

默认地址：`http://127.0.0.1:<port>`（默认端口见 `config.SERVICE_CONFIG.port`）

- **GET /**: 服务信息

  ```bash
  curl http://127.0.0.1:5001/
  ```

- **GET /health**: 健康检查

  ```bash
  curl http://127.0.0.1:5001/health
  ```

- **POST /query**: 代码检索

  请求体（示例）：
  ```bash
  curl -X POST http://127.0.0.1:5001/query \
    -H "Content-Type: application/json" \
    -d '{"query": "用户认证函数", "top_k": 10}'
  ```

  响应字段（简要）：
  - `query`: 原始查询
  - `results[]`: 检索结果数组，每项包含：
    - `file_path`、`text`、`score`
    - `metadata`: `file_name`、`file_type`、`start_line/end_line`、`code_insight`、`tags` 等

- **GET /stats**: 系统统计

  ```bash
  curl http://127.0.0.1:5001/stats
  ```
  返回包括：
  - `index_stats`: 分片总数、文件总数、语言/类型分布、时间戳等
  - `search_stats`: 平均时延、降级率等
  - `component_status`: 组件可用性

- **POST /indexes/update**: 增量刷新索引

  ```bash
  curl -X POST http://127.0.0.1:5001/indexes/update
  ```

- **POST /reindex**: 全量重建索引（当前返回未实现状态占位）

  ```bash
  curl -X POST http://127.0.0.1:5001/reindex
  ```

- **POST /codebase_insight**: 生成包含文件洞察的树状结构

  ```bash
  curl -X POST http://127.0.0.1:5001/codebase_insight \
    -H "Content-Type: application/json" \
    -d '{"include": ["src/**"], "exclude": ["**/tests/**"]}'
  ```

## 系统架构与流程

```
┌───────────────┐   ┌────────────────┐   ┌───────────────┐
│ CodeLoader    │ ─▶│ TreeSitter     │ ─▶│ CodeChunker   │
│ 文件加载/过滤 │   │ 语言解析/降级  │   │ 符号/函数分片 │
└───────────────┘   └────────────────┘   └───────────────┘
         │                         │                    │
         ▼                         ▼                    ▼
┌───────────────┐   ┌────────────────┐   ┌───────────────┐
│ InsightGen     │ ◀ │ KeywordExtract │ ◀ │ EmbeddingCalc  │
│ 代码洞察(LLM)  │   │ 关键词提取      │   │ 向量计算        │
└───────────────┘   └────────────────┘   └───────────────┘
         │
         ▼
┌─────────────────────────┐
│ RepoIndex 持久化/检索    │  metadata.json(.gz) + files/<hash>.json(.gz)
└─────────────────────────┘
         │
         ▼
┌──────────────────────────────────────────────────────┐
│ RetrievalEngine                                      │
│  QueryRewriter → RipgrepExecutor → ChunkMatcher      │
│  EmbeddingRetriever → LLMFilter → ResultProcessor    │
└──────────────────────────────────────────────────────┘
```

检索流程（当前默认以向量为主）：
1. 重写查询为向量检索友好的文本（必要时）
2. 计算查询向量，基于索引中分片向量做相似度召回
3. 可选：LLM 相关性筛选并打分
4. 排序、去重、多样化、截断内容并输出
5. 提供统计信息与可观测指标

## 数据模型（节选）

- `CodeChunk`: `id, relative_path, start_line/end_line, content, chunk_type, symbol_name, language, insight, keywords, embedding`
- `QueryRequest`: `query, top_k`
- `RetrievalResult`: `file_path, text, score, chunk_type, symbol_name, start_line, end_line`

## 配置说明（config.py）

- **SUPPORTED_LANGUAGES**: 语言与扩展名映射（含 `arkts: .ets`, `markdown`）
- **TREE_SITTER_CONFIG**: 分片粒度与符号类型（函数优先、`function_max_lines=80` 等）
- **RETRIEVAL_CONFIG**: `ripgrep_timeout`、`max_ripgrep_results`、`embedding_top_k`、`llm_filter_threshold` 等
- **SERVICE_CONFIG**: `host`、`port`、`workers`、`index_cache_dir`、`index_file_name`
- **INSIGHT_CONFIG**: 洞察提示词与批量参数
- **KEYWORD_CONFIG**: 关键词提取规则与权重
- **EMBEDDING_CONFIG**: 嵌入维度、并发、相似度阈值
- **INDEX_CONFIG**: 持久化、压缩与并发文件处理数
- **模型配置**: 在 `ModelManager` 中为 NORMAL/FLASH/EMBEDDING 设置 `base_url/model_name/api_key`。
  - 建议：将密钥放入环境变量并在 `config.py` 中读取后注入，避免明文提交。

## ArkTS 支持

- 解析器位于 `codebase_processor/arkts_parser.py`，依赖 `bin/arkts_tree_sitter/` 下特定平台动态库。
- 若库缺失或平台不匹配，则自动降级为简单解析；不影响整体索引与检索流程。

## 评测与基准

### 端到端评测工具

```bash
# 运行评测（推荐使用 CLI 参数覆盖默认路径）
python evaluation/end_to_end_evaluator.py --test-file evaluation/testset/testset_lite_3.jsonl
```

主要指标目标：P@1 > 80%，MRR@K > 70%。

### 代码质量检查

### Ruff 代码检查和格式化

项目使用 ruff 进行代码检查和格式化：

```bash
# 安装 ruff（已包含在 requirements.txt 中）
pip install ruff

# 运行代码检查
ruff check .

# 运行格式化检查
ruff format --check .

# 自动修复可修复的问题
ruff check --fix .

# 自动格式化代码
ruff format .

# 使用便捷脚本
./scripts/lint.sh    # 检查代码质量
./scripts/format.sh  # 格式化代码
```

### 单元测试

```bash
python tests/run_tests.py
```

## 故障排除

- **ripgrep 未安装**: 会自动降级为 `grep`；建议安装 ripgrep 以获得最佳性能
- **LLM/Embedding 失败**: 检查网络与 `config.py` 中模型配置；嵌入失败会回退为零向量并记录日志
- **索引太慢/内存不足**: 首次全量构建时间较长；可调小并发与批量，或启用增量更新与定时扫描
- **API 无返回或 503**: 可能索引未初始化或服务启动异常，查看 `--log-level DEBUG` 日志输出

## 目录结构

```
codebase_retriever/
├── main.py                    # 主程序入口
├── config.py                  # 配置文件
├── requirements.txt           # 依赖列表
├── README.md                  # 项目说明
├── codebase_processor/        # 代码库处理模块
│   ├── loader.py             # 文件加载器
│   ├── parser.py             # Tree-sitter解析器
│   └── chunker.py            # 代码分片器
├── insight_generator/         # 洞察生成模块
│   ├── code_insight.py       # 代码洞察生成器
│   └── keyword_extractor.py  # 关键词提取器
├── embedding_processor/       # 向量化模块
│   └── calculator.py         # 嵌入计算器
├── index_manager/            # 索引管理模块
│   ├── repo_index.py         # 仓库索引管理器
│   └── incremental_updater.py # 增量更新器
├── symbol_processor/          # 符号处理模块
│   ├── symbol_index.py       # 符号索引
│   ├── symbol_retriever.py   # 符号检索器
│   └── symbol_extractor.py   # 符号提取器
├── retrieval_service/        # 检索服务模块
│   ├── query_rewriter.py     # 查询重写器
│   ├── ripgrep_executor.py   # Ripgrep执行器
│   ├── chunk_matcher.py      # 分片匹配器
│   ├── embedding_retriever.py # 嵌入检索器
│   ├── llm_filter.py         # LLM筛选器
│   ├── result_processor.py   # 结果处理器
│   └── retrieval_engine.py   # 检索引擎
├── web_service/              # Web服务模块
│   └── api.py                # HTTP API接口
├── utils/                    # 工具模块
│   ├── models.py             # 数据模型
│   ├── interfaces.py         # 接口定义
│   └── helpers.py            # 辅助工具
└── tests/                    # 测试模块
    ├── test_chunker.py       # 分片器测试
    ├── test_keyword_extractor.py # 关键词提取测试
    ├── test_query_rewriter.py # 查询重写测试
    └── run_tests.py          # 测试运行器
```


## 符号检索（Symbol Retrieval）

系统支持对函数、方法、类等符号进行建模与检索，保留上下文结构（模块路径、命名空间、父符号等）。

- 符号索引文件：默认保存在 .codebase_index/<repo_id>/symbol_index.json（或 .gz）
- 自动构建：完整索引构建时自动提取符号并保存；若已有分片索引但缺少符号索引，会在服务启动时自动仅构建符号索引

### 符号作用域（scope）
- module_path：文件相对路径（如 src/auth/user.py），用于按模块（文件）维度过滤。
- namespace：父符号的命名链。例如类 Inner 的方法 foo，若类位于 Outer.Inner，则 foo 的 namespace 为 "Outer.Inner"。
- parent_symbol：父符号的ID，用于建立层级关系（如方法属于哪个类）。

说明：tree-sitter 路径下，类/接口/命名空间内的子符号会自动填充 namespace；简单解析（降级路径）下尽力填充，但在缺少块范围时可能为空。

### Web API

POST /symbol_query

请求 JSON:
- query: string（必填）
- exact_match: boolean（可选，默认 false）
- symbol_types: string[]（可选，取值见 utils.models.SymbolType，如 function/method/class/...）
- languages: string[]（可选）
- context_path: string（可选；模块/命名空间/类等路径片段，如 "Outer.Inner" 或 "auth"）
- include_references: boolean（可选）
- max_results: number（可选，默认 50）

返回 JSON（简化）：
- results[].symbol: { id, name, type, file_path, module_path, namespace, parent_symbol, signature, start_line, end_line, language }
- results[].score: number
- results[].match_reason: string

示例（按类上下文检索方法）：

curl -s -X POST 'http://127.0.0.1:5001/symbol_query' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "login",
    "symbol_types": ["method"],
    "languages": ["python", "typescript"],
    "context_path": "Outer.Inner",
    "max_results": 10
  }' | jq '.'

示例（按模块路径检索）：

curl -s -X POST 'http://127.0.0.1:5001/symbol_query' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "login",
    "symbol_types": ["function", "method"],
    "context_path": "auth",
    "max_results": 10
  }' | jq '.'


## 许可证

MIT License

## 更新日志

- v1.0.0
  - 初始版本：向量检索 + LLM 洞察/筛选 + 增量索引 + REST API
  - Tree-sitter 集成与 ArkTS 降级策略
  - 评测工具与常见指标输出
