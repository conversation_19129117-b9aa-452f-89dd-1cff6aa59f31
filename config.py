from pydantic import BaseModel, Field
from enum import Enum
from typing import Dict, Any, List
import openai
import asyncio


class IgnoreConfig(BaseModel):
    """文件和目录忽略配置"""

    # 隐藏目录
    hidden_directories: List[str] = [
        ".*",
        ".*/**",
        "**/.*",
    ]

    # 开发和构建相关目录
    ignore_directories: List[str] = [
        "**/__pycache__/**",
        "**/.pytest_cache/**",
        "**/venv/**",
        "**/node_modules/**",
        "**/dist/**",
        "**/build/**",
        "**/cache/**",
        "**/logs/**",
        "**/temp/**",
        "**/codebase_cache/**",
        "**/oh_modules/**",
    ]

    # 测试相关
    ignore_test_patterns: List[str] = [
        # "**/test/**",
        # "**/tests/**",
        # "**/test_*.*",
        # "**/*_test.*",
    ]

    # 版本控制和IDE
    ignore_vcs_ide: List[str] = [
        "*.git*",
        "*.vscode*",
        "*.venv*",
        "*.egg-info*",
    ]

    # 日志和覆盖率
    ignore_logs_coverage: List[str] = [
        "**/.coverage/**",
        "**/.coverage.*",
        "*.log",
        "*.log.*",
        "*logs",
    ]

    # 缓存和临时文件
    ignore_cache_temp: List[str] = [
        "*__pycache__*",
        "*.pytest_cache*",
        "*.cache",
        "*.pyc",
    ]

    # 媒体和数据文件
    ignore_media_data: List[str] = [
        "*.png",
        "*.jpeg",
        "*.jpg",
        "*.svg",
        "*.json",
        "*.json5",
        "*.sample",
    ]

    @property
    def all_ignore_patterns(self) -> List[str]:
        """获取所有忽略模式的合并列表"""
        return (
            self.hidden_directories
            + self.ignore_directories
            + self.ignore_test_patterns
            + self.ignore_vcs_ide
            + self.ignore_logs_coverage
            + self.ignore_cache_temp
            + self.ignore_media_data
        )


class ModelType(str, Enum):
    """模型类型枚举"""

    NORMAL = "normal"
    FLASH = "flash"
    EMBEDDING = "embedding"


class ModelConfig(BaseModel):
    """模型配置类"""

    model_name: str = Field(..., description="模型名称")
    base_url: str = Field(..., description="API基础URL")
    api_key: str = Field(..., description="API密钥")
    max_context_token: int = Field(default=128_000, description="最大输入token数")
    max_tokens: int = Field(default=1024, description="最大token数")
    timeout: int = Field(default=10, description="超时时间(秒)")
    extra_body: Dict[str, Any] = Field(default_factory=dict, description="额外请求参数")

    class Config:
        frozen = True


class LLMClient:
    """LLM客户端封装类"""

    def __init__(self, config: ModelConfig):
        self.config = config
        self._client = openai.AsyncOpenAI(
            api_key=config.api_key, base_url=config.base_url, timeout=config.timeout
        )

    async def chat_completion(self, prompt: str, **kwargs) -> str:
        """聊天补全"""
        try:
            response = await self._client.chat.completions.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=min(
                    self.config.max_tokens,
                    kwargs.get("max_tokens", self.config.max_tokens),
                ),
                extra_body=self.config.extra_body,
                # **kwargs
            )
            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"LLM调用失败: {str(e)}")

    async def embedding(self, text: str) -> list:
        """文本嵌入"""
        try:
            response = await self._client.embeddings.create(
                model=self.config.model_name,
                input=text,
            )
            return response.data[0].embedding
        except Exception as e:
            raise Exception(f"嵌入调用失败: {str(e)}")


class ModelManager:
    """模型管理器"""

    def __init__(self):
        self._configs: Dict[ModelType, ModelConfig] = {
            ModelType.NORMAL: ModelConfig(
                # model_name="kimi-k2-turbo-preview",
                # base_url="https://api.moonshot.cn/v1",
                # api_key="sk-h3VKycleDLMQiLrwQIiqBHItkHy2fXCyonOiEIoOeYycQjN3",
                # max_tokens=32_000,
                # timeout=30,
                model_name="doubao-seed-1-6-250615",
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key="ba1a55eb-f6ad-4bd0-88fb-4e3ab5f69722",
                max_tokens=32_000,
                timeout=30,
                extra_body={"thinking": {"type": "disabled"}},
            ),
            ModelType.FLASH: ModelConfig(
                model_name="doubao-seed-1-6-250615",
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key="ba1a55eb-f6ad-4bd0-88fb-4e3ab5f69722",
                max_tokens=32_000,
                timeout=30,
                extra_body={"thinking": {"type": "disabled"}},
            ),
            ModelType.EMBEDDING: ModelConfig(
                model_name="Qwen/Qwen3-Embedding-8B",
                base_url="https://api.siliconflow.cn/v1",
                api_key="sk-ravccrfhdceccblziftcgyzodzjbftdzrozbofbxrhayptnd",
                max_tokens=32_000,
                timeout=10,
                # model_name="doubao-embedding-large-text-250515",
                # base_url="https://ark.cn-beijing.volces.com/api/v3",
                # api_key="ba1a55eb-f6ad-4bd0-88fb-4e3ab5f69722",
                # max_tokens=32_000,
                # timeout=10,
            ),
            # ModelType.EMBEDDING: ModelConfig(
            #     model_name="text-embedding-v4",
            #     base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            #     api_key="sk-82d016a8f808468894626e0ec54a0d2a",
            #     max_tokens=32_000,
            #     timeout=10,
            # ),
        }
        self._clients: Dict[ModelType, LLMClient] = {}

    def get_client(self, model_type: ModelType) -> LLMClient:
        """获取模型客户端"""
        if model_type not in self._clients:
            self._clients[model_type] = LLMClient(self._configs[model_type])
        return self._clients[model_type]

    def get_config(self, model_type: ModelType) -> ModelConfig:
        """获取模型配置"""
        return self._configs[model_type]

    async def chat_completion(
        self, prompt: str, model_type: ModelType = ModelType.FLASH, **kwargs
    ) -> str:
        """聊天补全"""
        client = self.get_client(model_type)
        return await client.chat_completion(prompt, **kwargs)

    async def embedding(
        self, text: str, model_type: ModelType = ModelType.EMBEDDING
    ) -> list:
        """文本嵌入"""
        client = self.get_client(model_type)
        return await client.embedding(text)

    def list_models(self) -> list:
        """列出所有可用模型"""
        return [model_type.value for model_type in ModelType]


# 全局模型管理器实例
model_manager = ModelManager()

# 全局忽略配置实例
ignore_config = IgnoreConfig()


# 便捷函数
async def llm_call(prompt: str, model_type: str = "flash", **kwargs) -> str:
    """便捷的LLM调用函数"""
    return await model_manager.chat_completion(prompt, ModelType(model_type), **kwargs)


async def embedding_call(text: str, model_type: str = "embedding") -> list:
    """便捷的嵌入调用函数"""
    return await model_manager.embedding(text, ModelType(model_type))


def _run_async_in_new_loop(coro):
    """在新的事件循环中运行协程"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果当前线程已有运行的事件循环，创建新线程
            def run_in_thread():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(coro)
                finally:
                    new_loop.close()

            import concurrent.futures

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                return future.result()
        else:
            # 如果当前线程没有运行的事件循环，直接运行
            return loop.run_until_complete(coro)
    except Exception as e:
        raise Exception(f"异步执行失败: {str(e)}")


# 同步包装器（如果需要）
def sync_llm_call(prompt: str, model_type: str = "flash", **kwargs) -> str:
    """同步LLM调用函数"""
    return _run_async_in_new_loop(llm_call(prompt, model_type, **kwargs))


def sync_embedding_call(text: str, model_type: str = "embedding") -> list:
    """同步嵌入调用函数"""
    return _run_async_in_new_loop(embedding_call(text, model_type))


# 向后兼容的接口
def __llm_call(prompt: str, type: str = "flash") -> str:
    """向后兼容的LLM调用函数"""
    return sync_llm_call(prompt, type)


def __embedding_call(prompt: str, type: str = "embedding") -> list:
    """向后兼容的嵌入调用函数"""
    return sync_embedding_call(prompt, type)


# ============= Codebase Retriever 配置 =============

# 支持的编程语言配置
SUPPORTED_LANGUAGES = {
    "python": [".py"],
    "javascript": [".js"],
    "typescript": [".ts"],
    "java": [".java"],
    "c": [".c", ".h"],
    "cpp": [".cpp", ".cc", ".cxx", ".hpp", ".hxx"],
    "rust": [".rs"],
    "arkts": [".ets"],
    "markdown": [".md", ".markdown"],
}

# Tree-sitter配置
TREE_SITTER_CONFIG = {
    "chunk_min_lines": 3,
    "chunk_max_lines": 50,  # 减小最大行数，以函数粒度为主
    "function_max_lines": 80,  # 单个函数的最大行数，超过此值才会分割
    "prefer_function_granularity": True,  # 优先按函数粒度分片
    "symbol_types": [
        "function_definition",
        "class_definition",
        "method_definition",
        "interface_declaration",
    ],
    "context_lines": 1,  # 分片前后包含的上下文行数
}

# 检索配置
RETRIEVAL_CONFIG = {
    "ripgrep_timeout": 10,
    "max_ripgrep_results": 100,
    "embedding_top_k": 50,
    "llm_filter_threshold": 0.2,
    "max_query_rewrite_attempts": 3,
}

# 服务配置
SERVICE_CONFIG = {
    "host": "127.0.0.1",
    "port": 5001,
    "workers": 1,
    "index_cache_dir": ".codebase_index",
    "index_file_name": "repo_index.json",
}

# 洞察生成配置
INSIGHT_CONFIG = {
    "batch_size": 10,  # 增加批量处理的分片数量以提高并发
    "max_insight_length": 400,  # 洞察描述的最大长度
    "max_concurrent_requests": 30,  # 最大并发请求数
    "insight_prompt_template": """请为以下代码片段生成简洁的功能描述（不超过200字）：

文件路径: {file_path}
代码类型: {chunk_type}
符号名称: {symbol_name}

代码内容:
```{language}
{code_content}
```

请描述这段代码的主要功能和作用：
""",
}

# 关键词提取配置
KEYWORD_CONFIG = {
    "min_keyword_length": 2,
    "max_keywords_per_chunk": 20,
    "include_file_path_keywords": True,
    "keyword_weight": {
        "function_name": 1.0,
        "class_name": 1.0,
        "variable_name": 0.8,
        "file_path": 0.6,
    },
}

# 嵌入计算配置
EMBEDDING_CONFIG = {
    "batch_size": 10,  # 增加批量计算嵌入的数量
    "max_text_length": 32_000 * 2,  # 单个文本的最大长度
    "embedding_dimension": 1024,  # 嵌入向量维度（根据模型调整）
    "max_concurrent_requests": 20,  # 最大并发请求数
    "similarity_threshold": 0.25,  # 最低相似度阈值
}

# 索引管理配置
INDEX_CONFIG = {
    "enable_persistence": True,
    "compression_enabled": True,
    "max_concurrent_files": 10,  # 最大并发处理文件数
}

# ============= Symbol Retriever 配置 =============

# 符号提取配置
SYMBOL_CONFIG = {
    "extract_private_symbols": True,  # 是否提取私有符号
    "extract_local_variables": False,  # 是否提取局部变量
    "extract_imports": True,  # 是否提取导入信息
    "extract_comments": True,  # 是否提取注释信息
    "max_symbol_depth": 10,  # 最大符号嵌套深度
    "supported_languages": [  # 支持的语言列表
        "python",
        "javascript",
        "typescript",
        "java",
        "c",
        "cpp",
        "rust",
        "arkts",
    ],
    "symbol_types_by_language": {  # 每种语言支持的符号类型
        "python": ["function", "method", "class", "variable", "constant", "import"],
        "javascript": ["function", "method", "class", "variable", "constant", "import"],
        "typescript": [
            "function",
            "method",
            "class",
            "interface",
            "type_alias",
            "variable",
            "constant",
            "import",
        ],
        "java": ["method", "class", "interface", "field", "enum", "import"],
        "c": ["function", "variable", "constant"],
        "cpp": ["function", "method", "class", "variable", "constant", "namespace"],
        "rust": ["function", "method", "class", "enum", "constant", "module"],
        "arkts": ["function", "method", "class", "interface", "variable", "constant"],
    },
}

# 符号索引配置
SYMBOL_INDEX_CONFIG = {
    "enable_fuzzy_search": True,  # 启用模糊搜索
    "fuzzy_threshold": 0.8,  # 模糊匹配阈值
    "max_search_results": 100,  # 最大搜索结果数
    "enable_symbol_cache": True,  # 启用符号缓存
    "cache_size_limit": 10000,  # 缓存大小限制
    "index_update_batch_size": 1000,  # 批量更新大小
    "enable_reference_tracking": True,  # 启用引用追踪
    "max_reference_depth": 5,  # 最大引用深度
    "symbol_index_file": "symbol_index.json",  # 符号索引文件名
}

# 符号检索配置
SYMBOL_RETRIEVAL_CONFIG = {
    "default_max_results": 50,  # 默认最大结果数
    "enable_context_search": True,  # 启用上下文搜索
    "context_weight": 0.3,  # 上下文权重
    "name_match_weight": 0.7,  # 名称匹配权重
    "type_match_bonus": 0.1,  # 类型匹配加分
    "file_proximity_bonus": 0.05,  # 文件邻近性加分
    "search_timeout": 5.0,  # 搜索超时时间（秒）
    "enable_dependency_analysis": True,  # 启用依赖分析
    "max_dependency_depth": 3,  # 最大依赖深度
}
