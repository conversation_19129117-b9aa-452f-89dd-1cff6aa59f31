#!/usr/bin/env python3
"""
重新构建索引脚本
"""

import os
import shutil


def rebuild_index():
    """重新构建索引"""

    # 获取当前项目路径
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 默认缓存目录
    cache_dir = os.path.join(current_dir, "temp", "index_cache")

    print(f"当前目录: {current_dir}")
    print(f"缓存目录: {cache_dir}")

    # 检查缓存目录是否存在
    if os.path.exists(cache_dir):
        print("发现现有缓存，正在清除...")
        try:
            shutil.rmtree(cache_dir)
            print("✅ 缓存清除成功")
        except Exception as e:
            print(f"❌ 缓存清除失败: {e}")
            return False
    else:
        print("没有发现现有缓存")

    # 重新启动服务（使用--clear-cache参数）
    print("\n准备重新启动服务...")
    print("请手动运行以下命令来重新构建索引:")
    print(f"python main.py --repo-path {current_dir} --port 5001 --clear-cache")

    return True


if __name__ == "__main__":
    rebuild_index()
