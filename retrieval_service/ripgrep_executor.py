"""
Ripgrep执行器
执行ripgrep搜索命令并解析结果
"""

import subprocess
import re
from typing import List, Dict
from utils.interfaces import IRipgrepExecutor
from utils.models import SearchResult
from config import RETRIEVAL_CONFIG
import logging

logger = logging.getLogger(__name__)


class RipgrepExecutor(IRipgrepExecutor):
    """Ripgrep执行器实现"""

    def __init__(self):
        self.timeout = RETRIEVAL_CONFIG["ripgrep_timeout"]
        self.max_results = RETRIEVAL_CONFIG["max_ripgrep_results"]

        # 检查ripgrep是否可用
        self.ripgrep_available = self._check_ripgrep_availability()

    def _check_ripgrep_availability(self) -> bool:
        """检查ripgrep是否可用"""
        try:
            result = subprocess.run(
                ["rg", "--version"], capture_output=True, text=True, timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("ripgrep未安装或不可用，将使用grep降级")
            return False

    def execute_search(self, commands: List[str], repo_path: str) -> List[SearchResult]:
        """执行ripgrep搜索"""
        all_results = []

        for command in commands:
            if self.ripgrep_available:
                results = self._execute_ripgrep(command, repo_path)
            else:
                results = self._execute_grep_fallback(command, repo_path)

            all_results.extend(results)

            # 限制总结果数量
            if len(all_results) >= self.max_results:
                break

        # 去重并限制结果数量
        unique_results = self._deduplicate_results(all_results)
        return unique_results[: self.max_results]

    def _execute_ripgrep(self, pattern: str, repo_path: str) -> List[SearchResult]:
        """执行ripgrep命令"""
        try:
            # 构建ripgrep命令
            cmd = [
                "rg",
                "--line-number",  # 显示行号
                "--with-filename",  # 显示文件名
                "--no-heading",  # 不显示文件头
                "--context",
                "2",  # 显示上下文
                "--max-count",
                "10",  # 每个文件最多匹配10次
                "--type-add",
                "arkts:*.ets",  # 添加ArkTS支持
                pattern,
                repo_path,
            ]

            # 执行命令
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=self.timeout, cwd=repo_path
            )

            if result.returncode == 0:
                return self.parse_ripgrep_output(result.stdout)
            else:
                logger.warning(f"ripgrep搜索失败: {result.stderr}")
                return []

        except subprocess.TimeoutExpired:
            logger.warning(f"ripgrep搜索超时: {pattern}")
            return []
        except Exception as e:
            logger.error(f"ripgrep执行异常: {e}")
            return []

    def _execute_grep_fallback(
        self, pattern: str, repo_path: str
    ) -> List[SearchResult]:
        """使用grep作为降级方案"""
        try:
            # 构建grep命令
            cmd = [
                "grep",
                "-r",  # 递归搜索
                "-n",  # 显示行号
                "-H",  # 显示文件名
                "-A",
                "2",  # 显示后续2行
                "-B",
                "2",  # 显示前面2行
                "--include=*.py",
                "--include=*.js",
                "--include=*.ts",
                "--include=*.java",
                "--include=*.c",
                "--include=*.cpp",
                "--include=*.rs",
                "--include=*.md",
                "--include=*.ets",
                pattern,
                repo_path,
            ]

            # 执行命令
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=self.timeout, cwd=repo_path
            )

            if result.returncode == 0:
                return self._parse_grep_output(result.stdout, repo_path)
            else:
                return []

        except subprocess.TimeoutExpired:
            logger.warning(f"grep搜索超时: {pattern}")
            return []
        except Exception as e:
            logger.error(f"grep执行异常: {e}")
            return []

    def parse_ripgrep_output(self, output: str) -> List[SearchResult]:
        """解析ripgrep输出"""
        results = []

        if not output.strip():
            return results

        lines = output.strip().split("\n")
        current_context = []

        for line in lines:
            # ripgrep输出格式: file:line:content 或 file-line-content (上下文)
            if ":" in line:
                # 匹配行
                parts = line.split(":", 2)
                if len(parts) >= 3:
                    file_path = parts[0]
                    try:
                        line_number = int(parts[1])
                        matched_text = parts[2]

                        # 收集上下文
                        context = (
                            "\n".join(current_context[-2:]) if current_context else ""
                        )

                        result = SearchResult(
                            file_path=file_path,
                            line_number=line_number,
                            matched_text=matched_text,
                            context=context,
                        )
                        results.append(result)

                    except ValueError:
                        continue
            elif "-" in line:
                # 上下文行
                current_context.append(line)
                # 限制上下文长度
                if len(current_context) > 10:
                    current_context = current_context[-10:]

        return results

    def _parse_grep_output(self, output: str, repo_path: str) -> List[SearchResult]:
        """解析grep输出"""
        results = []

        if not output.strip():
            return results

        lines = output.strip().split("\n")

        for line in lines:
            # grep输出格式: file:line:content
            if ":" in line:
                parts = line.split(":", 2)
                if len(parts) >= 3:
                    file_path = parts[0]
                    try:
                        line_number = int(parts[1])
                        matched_text = parts[2]

                        # 将绝对路径转换为相对路径
                        if file_path.startswith(repo_path):
                            file_path = file_path[len(repo_path) :].lstrip("/")

                        result = SearchResult(
                            file_path=file_path,
                            line_number=line_number,
                            matched_text=matched_text,
                            context="",  # grep降级方案不提供上下文
                        )
                        results.append(result)

                    except ValueError:
                        continue

        return results

    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """去重搜索结果"""
        seen = set()
        unique_results = []

        for result in results:
            # 使用文件路径和行号作为唯一标识
            key = (result.file_path, result.line_number)
            if key not in seen:
                seen.add(key)
                unique_results.append(result)

        return unique_results

    def search_with_patterns(
        self, patterns: List[str], repo_path: str
    ) -> Dict[str, List[SearchResult]]:
        """使用多个模式搜索并返回分组结果"""
        pattern_results = {}

        for pattern in patterns:
            if self.ripgrep_available:
                results = self._execute_ripgrep(pattern, repo_path)
            else:
                results = self._execute_grep_fallback(pattern, repo_path)

            pattern_results[pattern] = results

        return pattern_results

    def get_search_stats(self, results: List[SearchResult]) -> Dict:
        """获取搜索结果统计信息"""
        if not results:
            return {"total_results": 0, "unique_files": 0, "file_distribution": {}}

        file_counts = {}
        for result in results:
            file_counts[result.file_path] = file_counts.get(result.file_path, 0) + 1

        return {
            "total_results": len(results),
            "unique_files": len(file_counts),
            "file_distribution": file_counts,
            "avg_results_per_file": len(results) / len(file_counts)
            if file_counts
            else 0,
        }

    def validate_pattern(self, pattern: str) -> bool:
        """验证搜索模式是否有效"""
        try:
            # 尝试编译正则表达式
            re.compile(pattern)
            return True
        except re.error:
            return False
