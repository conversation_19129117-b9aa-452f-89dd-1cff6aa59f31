"""
嵌入检索器
基于向量相似性进行代码分片检索
"""

from typing import List, Tuple
from utils.interfaces import IEmbeddingRetriever, IRepoIndex
from utils.models import CodeChunk
from utils.helpers import cosine_similarity
from embedding_processor.calculator import Embedding<PERSON>alculator
from config import RET<PERSON><PERSON>VAL_CONFIG, EMBEDDING_CONFIG
import time
import logging

logger = logging.getLogger(__name__)


class EmbeddingRetriever(IEmbeddingRetriever):
    """嵌入检索器实现"""

    def __init__(self):
        self.embedding_calculator = EmbeddingCalculator()
        self.embedding_top_k = RETRIEVAL_CONFIG["embedding_top_k"]
        self.similarity_threshold = EMBEDDING_CONFIG[
            "similarity_threshold"
        ]  # 最低相似度阈值

    async def retrieve_similar_chunks(
        self, query_embedding: List[float], index: IRepoIndex, top_k: int
    ) -> List[Tuple[CodeChunk, float]]:
        """基于嵌入向量检索相似分片"""
        if not query_embedding:
            return []

        similarities = []
        all_chunks = index.get_all_chunks()

        for chunk in all_chunks:
            if chunk.embedding:
                similarity = cosine_similarity(query_embedding, chunk.embedding)
                if similarity >= self.similarity_threshold:
                    similarities.append((chunk, similarity))

        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)

        return similarities[:top_k]

    async def search_by_query(
        self, query: str, index: IRepoIndex, top_k: int = None
    ) -> List[Tuple[CodeChunk, float]]:
        """根据查询文本检索相似分片"""
        if top_k is None:
            top_k = self.embedding_top_k

        # 计算查询的嵌入向量
        t0 = time.time()
        query_embedding = await self.embedding_calculator.calculate_embedding(query)
        logger.debug("calc query_embedding time: %s", time.time() - t0)

        if not query_embedding:
            return []

        return await self.retrieve_similar_chunks(query_embedding, index, top_k)

    async def search_by_chunk(
        self,
        reference_chunk: CodeChunk,
        index: IRepoIndex,
        top_k: int = None,
        exclude_self: bool = True,
    ) -> List[Tuple[CodeChunk, float]]:
        """根据参考分片检索相似分片"""
        if top_k is None:
            top_k = self.embedding_top_k

        if not reference_chunk.embedding:
            return []

        similarities = await self.retrieve_similar_chunks(
            reference_chunk.embedding, index, top_k + 1
        )

        # 排除自身
        if exclude_self:
            similarities = [
                (chunk, score)
                for chunk, score in similarities
                if chunk.id != reference_chunk.id
            ]

        return similarities[:top_k]

    def filter_by_language(
        self, chunk_similarities: List[Tuple[CodeChunk, float]], languages: List[str]
    ) -> List[Tuple[CodeChunk, float]]:
        """按编程语言过滤结果"""
        return [
            (chunk, score)
            for chunk, score in chunk_similarities
            if chunk.language in languages
        ]

    def filter_by_chunk_type(
        self, chunk_similarities: List[Tuple[CodeChunk, float]], chunk_types: List[str]
    ) -> List[Tuple[CodeChunk, float]]:
        """按分片类型过滤结果"""
        return [
            (chunk, score)
            for chunk, score in chunk_similarities
            if chunk.chunk_type in chunk_types
        ]

    def filter_by_file_pattern(
        self, chunk_similarities: List[Tuple[CodeChunk, float]], pattern: str
    ) -> List[Tuple[CodeChunk, float]]:
        """按文件路径模式过滤结果"""
        import re

        try:
            regex = re.compile(pattern, re.IGNORECASE)
            return [
                (chunk, score)
                for chunk, score in chunk_similarities
                if regex.search(chunk.relative_path)
            ]
        except re.error:
            # 如果正则表达式无效，使用简单字符串匹配
            return [
                (chunk, score)
                for chunk, score in chunk_similarities
                if pattern.lower() in chunk.relative_path.lower()
            ]

    def group_by_file(self, chunk_similarities: List[Tuple[CodeChunk, float]]) -> dict:
        """按文件分组检索结果"""
        file_groups = {}

        for chunk, score in chunk_similarities:
            file_path = chunk.relative_path
            if file_path not in file_groups:
                file_groups[file_path] = []
            file_groups[file_path].append((chunk, score))

        # 按分数排序每个文件的结果
        for file_path in file_groups:
            file_groups[file_path].sort(key=lambda x: x[1], reverse=True)

        return file_groups

    def diversify_results(
        self, chunk_similarities: List[Tuple[CodeChunk, float]], max_per_file: int = 2
    ) -> List[Tuple[CodeChunk, float]]:
        """多样化结果，避免同一文件的结果过多"""
        file_counts = {}
        diversified_results = []

        for chunk, score in chunk_similarities:
            file_path = chunk.relative_path
            file_count = file_counts.get(file_path, 0)

            if file_count < max_per_file:
                diversified_results.append((chunk, score))
                file_counts[file_path] = file_count + 1

        return diversified_results

    def boost_by_keywords(
        self,
        chunk_similarities: List[Tuple[CodeChunk, float]],
        query_keywords: List[str],
        boost_factor: float = 0.1,
    ) -> List[Tuple[CodeChunk, float]]:
        """根据关键词匹配提升分数"""
        if not query_keywords:
            return chunk_similarities

        boosted_results = []
        query_keywords_lower = [kw.lower() for kw in query_keywords]

        for chunk, score in chunk_similarities:
            boost = 0.0

            if chunk.keywords:
                chunk_keywords_lower = [kw.lower() for kw in chunk.keywords]
                matched_keywords = set(query_keywords_lower) & set(chunk_keywords_lower)
                boost = len(matched_keywords) * boost_factor

            # 检查符号名称匹配
            if chunk.symbol_name:
                symbol_lower = chunk.symbol_name.lower()
                for query_kw in query_keywords_lower:
                    if query_kw in symbol_lower:
                        boost += boost_factor

            boosted_score = min(score + boost, 1.0)  # 确保分数不超过1.0
            boosted_results.append((chunk, boosted_score))

        # 重新排序
        boosted_results.sort(key=lambda x: x[1], reverse=True)

        return boosted_results

    def get_retrieval_stats(
        self, chunk_similarities: List[Tuple[CodeChunk, float]]
    ) -> dict:
        """获取检索统计信息"""
        if not chunk_similarities:
            return {
                "total_results": 0,
                "avg_similarity": 0.0,
                "max_similarity": 0.0,
                "min_similarity": 0.0,
                "unique_files": 0,
                "language_distribution": {},
                "type_distribution": {},
            }

        scores = [score for _, score in chunk_similarities]
        chunks = [chunk for chunk, _ in chunk_similarities]

        # 语言分布
        language_dist = {}
        for chunk in chunks:
            lang = chunk.language
            language_dist[lang] = language_dist.get(lang, 0) + 1

        # 类型分布
        type_dist = {}
        for chunk in chunks:
            chunk_type = chunk.chunk_type
            type_dist[chunk_type] = type_dist.get(chunk_type, 0) + 1

        return {
            "total_results": len(chunk_similarities),
            "avg_similarity": sum(scores) / len(scores),
            "max_similarity": max(scores),
            "min_similarity": min(scores),
            "unique_files": len(set(chunk.relative_path for chunk in chunks)),
            "language_distribution": language_dist,
            "type_distribution": type_dist,
        }

    async def hybrid_search(
        self,
        query: str,
        ripgrep_chunks: List[CodeChunk],
        index: IRepoIndex,
        embedding_weight: float = 0.7,
    ) -> List[Tuple[CodeChunk, float]]:
        """混合搜索：结合ripgrep结果和嵌入检索"""
        # 获取嵌入检索结果
        embedding_results = await self.search_by_query(
            query, index, self.embedding_top_k
        )

        # 创建分片分数映射
        chunk_scores = {}

        # 添加ripgrep结果（给予基础分数）
        for chunk in ripgrep_chunks:
            chunk_scores[chunk.id] = 0.3  # 基础分数

        # 添加嵌入检索结果
        for chunk, similarity in embedding_results:
            if chunk.id in chunk_scores:
                # 如果ripgrep也找到了这个分片，结合分数
                chunk_scores[chunk.id] = (
                    chunk_scores[chunk.id] * (1 - embedding_weight)
                    + similarity * embedding_weight
                )
            else:
                # 纯嵌入检索结果
                chunk_scores[chunk.id] = similarity * embedding_weight

        # 转换为结果列表
        all_chunks = {chunk.id: chunk for chunk in ripgrep_chunks}
        for chunk, _ in embedding_results:
            all_chunks[chunk.id] = chunk

        hybrid_results = [
            (all_chunks[chunk_id], score) for chunk_id, score in chunk_scores.items()
        ]

        # 按分数排序
        hybrid_results.sort(key=lambda x: x[1], reverse=True)

        return hybrid_results
