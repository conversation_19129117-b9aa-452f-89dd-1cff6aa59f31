"""
检索引擎
集成完整的检索流程
"""

import time
from typing import List, Dict
from utils.models import RetrievalResult
from index_manager.repo_index import RepoIndex
from retrieval_service.query_rewriter import QueryRewriter
from retrieval_service.ripgrep_executor import RipgrepExecutor
from retrieval_service.chunk_matcher import ChunkMatcher
from retrieval_service.embedding_retriever import Embedding<PERSON>etriever
from retrieval_service.llm_filter import LL<PERSON>ilter
from retrieval_service.result_processor import ResultProcessor
import logging

logger = logging.getLogger(__name__)


class RetrievalEngine:
    """检索引擎"""

    def __init__(self, repo_index: RepoIndex, repo_path: str):
        self.repo_index = repo_index
        self.repo_path = repo_path

        # 初始化各个组件
        self.query_rewriter = QueryRewriter()
        self.ripgrep_executor = RipgrepExecutor()
        self.chunk_matcher = ChunkMatcher()
        self.embedding_retriever = EmbeddingRetriever()
        self.llm_filter = LLMFilter()
        self.result_processor = ResultProcessor()

        # 执行统计
        self.search_stats = {
            "total_searches": 0,
            "avg_response_time": 0.0,
            "ripgrep_success_rate": 0.0,
            "embedding_fallback_rate": 0.0,
        }

    async def search(
        self,
        query: str,
        top_k: int = 10,
        use_llm_filter: bool = True,
        embedding_weight: float = 0.7,
    ) -> List[RetrievalResult]:
        """执行完整的检索流程"""
        start_time = time.time()

        try:
            logger.info(f"开始检索: {query}")

            # 阶段1: 查询重写
            stage1_start = time.time()
            ripgrep_chunks = []
            if 0:
                repo_insight = self.repo_index.get_repo_insight()

                # 获取仓库关键词列表
                keyword_list = self._get_repo_keywords()

                ripgrep_patterns = await self.query_rewriter.rewrite_to_ripgrep(
                    query, repo_insight, keyword_list
                )
                stage1_time = (time.time() - stage1_start) * 1000

                logger.debug("ripgrep_patterns: %s", ripgrep_patterns)
                logger.info(
                    f"查询重写完成: 生成 {len(ripgrep_patterns)} 个ripgrep模式查询"
                )

                # 阶段2: Ripgrep搜索
                stage2_start = time.time()
                search_results = self.ripgrep_executor.execute_search(
                    ripgrep_patterns, self.repo_path
                )
                stage2_time = (time.time() - stage2_start) * 1000

                logger.info(f"Ripgrep搜索完成: {len(search_results)} 个搜索结果")

                # 阶段3: 分片匹配
                stage3_start = time.time()
                ripgrep_chunks = self.chunk_matcher.match_chunks(
                    search_results, self.repo_index
                )
                stage3_time = (time.time() - stage3_start) * 1000

                logger.info(f"分片匹配完成: {len(ripgrep_chunks)} 个匹配分片")

            # 阶段4: 关键字匹配
            pass

            # 阶段4: 嵌入检索（如果ripgrep结果不足或作为补充）
            stage4_start = time.time()
            embedding_chunks = []

            if 1:  # if len(ripgrep_chunks) < top_k:
                # Ripgrep结果不足，使用嵌入检索补充
                embedding_query = self.query_rewriter.rewrite_to_embedding_query(query)
                logger.debug("embedding_query: %s", embedding_query)
                embedding_results = await self.embedding_retriever.search_by_query(
                    embedding_query, self.repo_index, int(top_k * 2.5)
                )
                embedding_chunks = [chunk for chunk, _ in embedding_results]
                logger.info(f"嵌入检索补充: {len(embedding_chunks)} 个分片")

            stage4_time = (time.time() - stage4_start) * 1000

            # 阶段5: 结果合并
            stage5_start = time.time()

            # 合并ripgrep和嵌入检索结果
            all_chunks = ripgrep_chunks + embedding_chunks

            # 去重
            unique_chunks = list({chunk.id: chunk for chunk in all_chunks}.values())

            stage5_time = (time.time() - stage5_start) * 1000

            logger.info(f"结果合并完成: {len(unique_chunks)} 个唯一分片")

            # 阶段6: LLM筛选（可选）
            stage6_start = time.time()

            if use_llm_filter and len(unique_chunks) > top_k:
                filtered_results = await self.llm_filter.filter_chunks(
                    query, unique_chunks
                )
                final_chunks = [chunk for chunk, score in filtered_results]
                chunk_scores = filtered_results
            else:
                # 不使用LLM筛选，给予默认分数
                final_chunks = unique_chunks
                chunk_scores = [(chunk, 0.8) for chunk in final_chunks]

            stage6_time = (time.time() - stage6_start) * 1000

            logger.info(f"LLM筛选完成: {len(final_chunks)} 个相关分片")

            # 阶段7: 结果排序和格式化
            stage7_start = time.time()

            # 排序和去重
            sorted_results = self.result_processor.sort_and_deduplicate(chunk_scores)

            # 多样化结果
            diversified_results = self.result_processor.diversify_by_file(
                sorted_results, max_per_file=3
            )

            # 格式化输出
            final_results = self.result_processor.format_results(
                diversified_results, top_k
            )

            stage7_time = (time.time() - stage7_start) * 1000

            # 更新统计信息
            total_time = (time.time() - start_time) * 1000
            # self._update_search_stats(total_time, len(search_results) > 0)

            logger.info(
                f"检索完成: 返回 {len(final_results)} 个结果，总耗时 {total_time:.2f}ms"
            )

            # 记录详细的执行时间
            self._log_execution_stages(
                {
                    "query_rewrite": 0,  # stage1_time,
                    "ripgrep_search": 0,  # stage2_time,
                    "chunk_matching": 0,  # stage3_time,
                    "embedding_retrieval": stage4_time,
                    "result_merging": stage5_time,
                    "llm_filtering": stage6_time,
                    "result_processing": stage7_time,
                    "total_time": total_time,
                }
            )

            return final_results

        except Exception as e:
            logger.exception(f"检索过程异常: {e}")
            # 返回空结果而不是抛出异常
            return []

    async def search_with_fallback(
        self, query: str, top_k: int = 10
    ) -> List[RetrievalResult]:
        """带降级策略的检索"""
        try:
            # 首先尝试完整流程
            results = await self.search(query, top_k, use_llm_filter=True)

            if results:
                return results

            logger.warning("完整流程无结果，尝试降级策略...")

            # 降级策略1: 不使用LLM筛选
            results = await self.search(query, top_k, use_llm_filter=False)

            if results:
                return results

            # 降级策略2: 纯嵌入检索
            embedding_results = await self.embedding_retriever.search_by_query(
                query, self.repo_index, top_k
            )

            if embedding_results:
                chunk_scores = embedding_results
                formatted_results = self.result_processor.format_results(
                    chunk_scores, top_k
                )
                return formatted_results

            # 最后降级: 返回空结果
            return []

        except Exception as e:
            logger.error(f"降级检索也失败: {e}")
            return []

    def get_index_stats(self) -> Dict:
        """获取索引统计信息"""
        return self.repo_index.get_stats()

    def get_detailed_stats(self) -> Dict:
        """获取详细统计信息"""
        index_stats = self.repo_index.get_stats()

        return {
            "index_stats": index_stats,
            "search_stats": self.search_stats,
            "component_status": {
                "ripgrep_available": self.ripgrep_executor.ripgrep_available,
                "embedding_calculator": "available",
                "llm_filter": "available",
            },
        }

    async def refresh_index(self) -> Dict:
        """刷新索引 - 增量更新"""
        from index_manager.incremental_updater import IncrementalUpdater

        updater = IncrementalUpdater(self.repo_index)
        update_stats = await updater.update_index(self.repo_path)

        return {
            "status": "completed",
            "message": "索引刷新完成",
            "update_stats": update_stats,
        }

    async def reindex(self) -> Dict:
        """重新索引 - 完全重建"""
        # 这里应该调用主程序的重新索引逻辑
        # 由于架构限制，这里只返回当前状态
        return {
            "status": "not_implemented",
            "message": "完全重建索引功能需要在主程序中实现",
        }

    def _get_repo_keywords(self) -> List[str]:
        return []
        """获取仓库的关键词列表"""
        all_keywords = set()

        # 从所有分片中收集关键词
        for chunk in self.repo_index.get_all_chunks():
            if chunk.keywords:
                all_keywords.update(chunk.keywords)

        # 按频率排序（简单实现：按字母顺序）
        return sorted(list(all_keywords))

    def _update_search_stats(self, response_time: float, ripgrep_success: bool):
        """更新搜索统计"""
        self.search_stats["total_searches"] += 1

        # 更新平均响应时间
        total_searches = self.search_stats["total_searches"]
        current_avg = self.search_stats["avg_response_time"]
        self.search_stats["avg_response_time"] = (
            current_avg * (total_searches - 1) + response_time
        ) / total_searches

        # 更新ripgrep成功率
        if ripgrep_success:
            current_success_count = self.search_stats["ripgrep_success_rate"] * (
                total_searches - 1
            )
            self.search_stats["ripgrep_success_rate"] = (
                current_success_count + 1
            ) / total_searches
        else:
            current_success_count = self.search_stats["ripgrep_success_rate"] * (
                total_searches - 1
            )
            self.search_stats["ripgrep_success_rate"] = (
                current_success_count / total_searches
            )

            # 更新嵌入降级率
            current_fallback_count = self.search_stats["embedding_fallback_rate"] * (
                total_searches - 1
            )
            self.search_stats["embedding_fallback_rate"] = (
                current_fallback_count + 1
            ) / total_searches

    def _log_execution_stages(self, stages: Dict[str, float]):
        """记录执行阶段时间"""
        logger.info("执行阶段耗时:")
        for stage, time_ms in stages.items():
            logger.info(f"  {stage}: {time_ms:.2f}ms")

    async def test_components(self) -> Dict:
        """测试各个组件的可用性"""
        test_results = {}

        # 测试查询重写
        try:
            patterns = await self.query_rewriter.rewrite_to_ripgrep(
                "test query", "test repo"
            )
            test_results["query_rewriter"] = "ok" if patterns else "no_patterns"
        except Exception as e:
            test_results["query_rewriter"] = f"error: {e}"

        # 测试ripgrep
        try:
            results = self.ripgrep_executor.execute_search(["test"], self.repo_path)
            test_results["ripgrep_executor"] = "ok"
        except Exception as e:
            test_results["ripgrep_executor"] = f"error: {e}"

        # 测试嵌入检索
        try:
            results = await self.embedding_retriever.search_by_query(
                "test", self.repo_index, 1
            )
            test_results["embedding_retriever"] = "ok"
        except Exception as e:
            test_results["embedding_retriever"] = f"error: {e}"

        return test_results
