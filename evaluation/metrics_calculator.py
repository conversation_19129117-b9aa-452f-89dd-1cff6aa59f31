"""
评估指标计算器
计算P@K, MRR@K, nDCG@K, F1等评估指标
"""

import math
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class EvaluationResult:
    """单个查询的评估结果"""

    query: str
    ground_truth: str
    predicted_files: List[str]
    scores: List[float]

    # 指标
    precision_at_k: Dict[int, float]
    recall_at_k: Dict[int, float]
    f1_at_k: Dict[int, float]
    mrr: float
    ndcg_at_k: Dict[int, float]

    # 匹配信息
    is_exact_match: bool
    match_rank: Optional[int]  # 第一个匹配的排名（1-based），None表示无匹配
    actual_k: int  # 实际返回的结果数量


@dataclass
class ComparisonResult:
    """路径匹配vs内容匹配的对比结果"""

    query: str
    ground_truth: str
    predicted_files: List[str]
    scores: List[float]

    # 路径匹配结果
    path_matching: EvaluationResult

    # 内容匹配结果
    content_matching: EvaluationResult


@dataclass
class AggregatedMetrics:
    """聚合评估指标"""

    total_queries: int

    # 平均指标
    avg_precision_at_k: Dict[int, float]
    avg_recall_at_k: Dict[int, float]
    avg_f1_at_k: Dict[int, float]
    avg_mrr: float
    avg_ndcg_at_k: Dict[int, float]

    # 匹配统计
    exact_match_count: int
    exact_match_rate: float
    queries_with_results: int
    queries_with_results_rate: float

    # K值统计
    avg_k: float  # 平均输出K值
    success_rate_at_k: Dict[int, float]  # 成功率@K (包括@k)

    # 时延统计
    avg_query_time_ms: float
    max_query_time_ms: float
    min_query_time_ms: float


class MetricsCalculator:
    """评估指标计算器"""

    def __init__(self, k_values: List[int] = None):
        """
        初始化评估指标计算器

        Args:
            k_values: 要计算的K值列表，默认为[1, 3, 5, 10]
        """
        self.k_values = k_values or [1, 3, 5, 10]
        self.max_k = max(self.k_values)  # 记录最大K值，用于MRR@K计算

    def calculate_single_query_metrics(
        self,
        query: str,
        ground_truth_file: str,
        predicted_files: List[str],
        scores: List[float] = None,
        ground_truth_content: str = None,
        predicted_contents: List[str] = None,
    ) -> EvaluationResult:
        """
        计算单个查询的评估指标

        Args:
            query: 查询文本
            ground_truth_file: 正确答案文件路径
            predicted_files: 预测的文件路径列表
            scores: 对应的分数列表
            ground_truth_content: 正确答案文件内容（可选，用于内容匹配）
            predicted_contents: 预测文件内容列表（可选，用于内容匹配）

        Returns:
            评估结果
        """
        if scores is None:
            scores = [1.0] * len(predicted_files)

        # 标准化文件路径（移除开头的斜杠，统一路径分隔符）
        ground_truth_normalized = self._normalize_path(ground_truth_file)
        predicted_normalized = [self._normalize_path(path) for path in predicted_files]

        # 计算匹配信息 - 优先使用内容匹配，回退到路径匹配
        is_exact_match = False
        match_rank = None

        if ground_truth_content is not None and predicted_contents is not None:
            # 基于内容匹配
            is_exact_match, match_rank = self._check_content_match(
                ground_truth_content, predicted_contents
            )
        else:
            # 回退到路径匹配
            is_exact_match = ground_truth_normalized in predicted_normalized
            if is_exact_match:
                match_rank = predicted_normalized.index(ground_truth_normalized) + 1

        # 计算各K值的指标
        precision_at_k = {}
        recall_at_k = {}
        f1_at_k = {}
        ndcg_at_k = {}

        # 扩展k_values以包含可能需要的其他k值
        extended_k_values = self.k_values.copy()
        if len(predicted_files) not in extended_k_values:
            extended_k_values.append(len(predicted_files))
        extended_k_values = sorted(set(extended_k_values))

        for k in extended_k_values:
            # 取前K个结果（如果结果不足K个，就取所有结果）
            actual_k = min(k, len(predicted_normalized))

            if actual_k == 0:
                # 如果没有搜索结果
                precision_at_k[k] = 0.0
                recall_at_k[k] = 0.0
                f1_at_k[k] = 0.0
                ndcg_at_k[k] = 0.0
            else:
                top_k_predicted = predicted_normalized[:actual_k]
                top_k_scores = scores[:actual_k]

                # 计算Precision@K和Recall@K
                if ground_truth_content is not None and predicted_contents is not None:
                    # 基于内容匹配
                    top_k_contents = (
                        predicted_contents[:actual_k] if predicted_contents else []
                    )
                    relevant_in_top_k = (
                        1
                        if self._is_content_relevant(
                            ground_truth_content, top_k_contents
                        )
                        else 0
                    )
                else:
                    # 基于路径匹配
                    relevant_in_top_k = (
                        1 if ground_truth_normalized in top_k_predicted else 0
                    )

                # P@K = 相关文档数 / min(K, 实际结果数)
                precision_at_k[k] = relevant_in_top_k / actual_k
                recall_at_k[k] = relevant_in_top_k  # 因为只有一个相关文档

                # 计算F1@K
                if precision_at_k[k] + recall_at_k[k] > 0:
                    f1_at_k[k] = (
                        2
                        * precision_at_k[k]
                        * recall_at_k[k]
                        / (precision_at_k[k] + recall_at_k[k])
                    )
                else:
                    f1_at_k[k] = 0.0

                # 计算nDCG@K
                if ground_truth_content is not None and predicted_contents is not None:
                    top_k_contents = (
                        predicted_contents[:actual_k] if predicted_contents else []
                    )
                    ndcg_at_k[k] = self._calculate_ndcg_at_k_content(
                        ground_truth_content, top_k_contents, top_k_scores, actual_k
                    )
                else:
                    ndcg_at_k[k] = self._calculate_ndcg_at_k(
                        ground_truth_normalized, top_k_predicted, top_k_scores, actual_k
                    )

        # 计算MRR@K (限制在最大K值内)
        if match_rank and match_rank <= self.max_k:
            mrr = 1.0 / match_rank
        else:
            mrr = 0.0

        # 记录实际返回的结果数量
        actual_k = len(predicted_files)

        return EvaluationResult(
            query=query,
            ground_truth=ground_truth_file,
            predicted_files=predicted_files,
            scores=scores,
            precision_at_k=precision_at_k,
            recall_at_k=recall_at_k,
            f1_at_k=f1_at_k,
            mrr=mrr,
            ndcg_at_k=ndcg_at_k,
            is_exact_match=is_exact_match,
            match_rank=match_rank,
            actual_k=actual_k,
        )

    def calculate_comparison_metrics(
        self,
        query: str,
        ground_truth_file: str,
        predicted_files: List[str],
        scores: List[float] = None,
        ground_truth_content: str = None,
        predicted_contents: List[str] = None,
    ) -> ComparisonResult:
        """
        同时计算路径匹配和内容匹配的评估指标，用于对比分析

        Args:
            query: 查询文本
            ground_truth_file: 正确答案文件路径
            predicted_files: 预测的文件路径列表
            scores: 对应的分数列表
            ground_truth_content: 正确答案文件内容
            predicted_contents: 预测文件内容列表

        Returns:
            对比评估结果
        """
        # 计算路径匹配结果
        path_result = self.calculate_single_query_metrics(
            query=query,
            ground_truth_file=ground_truth_file,
            predicted_files=predicted_files,
            scores=scores,
            ground_truth_content=None,  # 强制使用路径匹配
            predicted_contents=None,
        )

        # 计算内容匹配结果
        content_result = self.calculate_single_query_metrics(
            query=query,
            ground_truth_file=ground_truth_file,
            predicted_files=predicted_files,
            scores=scores,
            ground_truth_content=ground_truth_content,
            predicted_contents=predicted_contents,
        )

        return ComparisonResult(
            query=query,
            ground_truth=ground_truth_file,
            predicted_files=predicted_files,
            scores=scores or [1.0] * len(predicted_files),
            path_matching=path_result,
            content_matching=content_result,
        )

    def _check_content_match(
        self, ground_truth_content: str, predicted_contents: List[str]
    ) -> Tuple[bool, Optional[int]]:
        """
        检查ground truth内容是否在预测结果中

        Args:
            ground_truth_content: 正确答案内容
            predicted_contents: 预测结果内容列表

        Returns:
            (是否匹配, 匹配排名)
        """
        if not ground_truth_content or not predicted_contents:
            return False, None

        # 标准化内容（移除空白字符差异）
        gt_normalized = self._normalize_content(ground_truth_content)

        for i, pred_content in enumerate(predicted_contents):
            if pred_content and gt_normalized in self._normalize_content(pred_content):
                return True, i + 1

        return False, None

    def _is_content_relevant(
        self, ground_truth_content: str, predicted_contents: List[str]
    ) -> bool:
        """
        检查ground truth内容是否在预测结果内容列表中

        Args:
            ground_truth_content: 正确答案内容
            predicted_contents: 预测结果内容列表

        Returns:
            是否相关
        """
        if not ground_truth_content or not predicted_contents:
            return False

        gt_normalized = self._normalize_content(ground_truth_content)

        for pred_content in predicted_contents:
            if pred_content and gt_normalized in self._normalize_content(pred_content):
                return True

        return False

    def _normalize_content(self, content: str) -> str:
        """
        标准化内容，用于匹配比较

        Args:
            content: 原始内容

        Returns:
            标准化后的内容
        """
        if not content:
            return ""

        # 移除多余的空白字符，统一换行符
        normalized = " ".join(content.split())
        return normalized.lower()

    def _calculate_ndcg_at_k_content(
        self,
        ground_truth_content: str,
        predicted_contents: List[str],
        scores: List[float],
        k: int,
    ) -> float:
        """
        基于内容匹配计算nDCG@K

        Args:
            ground_truth_content: 正确答案内容
            predicted_contents: 预测结果内容列表
            scores: 分数列表
            k: K值

        Returns:
            nDCG@K值
        """
        if not ground_truth_content or not predicted_contents:
            return 0.0

        gt_normalized = self._normalize_content(ground_truth_content)

        # 计算DCG@K
        dcg = 0.0
        found_match = False
        for i, pred_content in enumerate(predicted_contents[:k]):
            if pred_content and gt_normalized in self._normalize_content(pred_content):
                # 对于单个正确答案的场景，只计算第一个匹配
                if not found_match:
                    relevance = 1.0
                    dcg += relevance / math.log2(i + 2)  # i+2 因为log2(1)=0
                    found_match = True
                    break  # 找到第一个匹配就停止

        # 计算IDCG@K（理想情况下的DCG）
        # 对于单个相关文档，理想情况是它在第1位
        idcg = 1.0 / math.log2(2)  # = 1.0

        # 计算nDCG@K
        if idcg > 0:
            return dcg / idcg
        else:
            return 0.0

    def _normalize_path(self, file_path: str) -> str:
        """
        标准化文件路径

        Args:
            file_path: 原始文件路径

        Returns:
            标准化后的路径
        """
        if not file_path:
            return ""

        # 移除开头的斜杠
        path = file_path.lstrip("/")

        # 统一使用正斜杠
        path = path.replace("\\", "/")

        return path

    def _calculate_ndcg_at_k(
        self, ground_truth: str, predicted: List[str], scores: List[float], k: int
    ) -> float:
        """
        计算nDCG@K

        Args:
            ground_truth: 正确答案
            predicted: 预测结果列表
            scores: 分数列表
            k: K值

        Returns:
            nDCG@K值
        """
        # 计算DCG@K
        dcg = 0.0
        for i, pred_file in enumerate(predicted[:k]):
            if pred_file == ground_truth:
                # 相关性为1，不相关为0
                relevance = 1.0
                dcg += relevance / math.log2(i + 2)  # i+2 因为log2(1)=0
                break  # 找到匹配就停止（路径匹配不会有重复）

        # 计算IDCG@K（理想情况下的DCG）
        # 对于单个相关文档，理想情况是它在第1位
        idcg = 1.0 / math.log2(2)  # = 1.0

        # 计算nDCG@K
        if idcg > 0:
            return dcg / idcg
        else:
            return 0.0

    def aggregate_metrics(
        self,
        evaluation_results: List[EvaluationResult],
        query_times_ms: List[float] = None,
    ) -> AggregatedMetrics:
        """
        聚合多个查询的评估指标

        Args:
            evaluation_results: 评估结果列表
            query_times_ms: 查询时间列表（毫秒）

        Returns:
            聚合指标
        """
        if not evaluation_results:
            return self._create_empty_aggregated_metrics()

        total_queries = len(evaluation_results)

        # 计算平均K值
        k_values = [result.actual_k for result in evaluation_results]
        avg_k = sum(k_values) / total_queries if k_values else 0.0

        # 聚合各K值的指标
        avg_precision_at_k = {}
        avg_recall_at_k = {}
        avg_f1_at_k = {}
        avg_ndcg_at_k = {}
        success_rate_at_k = {}

        # 添加实际平均K值到k_values列表中进行计算
        k_values_to_calculate = self.k_values.copy()
        avg_k_int = int(round(avg_k))
        if avg_k_int not in k_values_to_calculate:
            k_values_to_calculate.append(avg_k_int)
        k_values_to_calculate = sorted(k_values_to_calculate)

        for k in k_values_to_calculate:
            precision_values = [
                result.precision_at_k.get(k, 0.0) for result in evaluation_results
            ]
            recall_values = [
                result.recall_at_k.get(k, 0.0) for result in evaluation_results
            ]
            f1_values = [result.f1_at_k.get(k, 0.0) for result in evaluation_results]
            ndcg_values = [
                result.ndcg_at_k.get(k, 0.0) for result in evaluation_results
            ]

            avg_precision_at_k[k] = sum(precision_values) / total_queries
            avg_recall_at_k[k] = sum(recall_values) / total_queries
            avg_f1_at_k[k] = sum(f1_values) / total_queries
            avg_ndcg_at_k[k] = sum(ndcg_values) / total_queries

            # 计算成功率@K (有多少查询在前K个结果中找到了正确答案)
            success_count = sum(
                1
                for result in evaluation_results
                if result.match_rank and result.match_rank <= k
            )
            success_rate_at_k[k] = success_count / total_queries

        # 计算平均MRR@K
        mrr_values = [result.mrr for result in evaluation_results]
        avg_mrr = sum(mrr_values) / total_queries

        # 计算匹配统计
        exact_match_count = sum(
            1 for result in evaluation_results if result.is_exact_match
        )
        exact_match_rate = exact_match_count / total_queries

        queries_with_results = sum(
            1 for result in evaluation_results if result.predicted_files
        )
        queries_with_results_rate = queries_with_results / total_queries

        # 计算时延统计
        if query_times_ms:
            avg_query_time_ms = sum(query_times_ms) / len(query_times_ms)
            max_query_time_ms = max(query_times_ms)
            min_query_time_ms = min(query_times_ms)
        else:
            avg_query_time_ms = 0.0
            max_query_time_ms = 0.0
            min_query_time_ms = 0.0

        return AggregatedMetrics(
            total_queries=total_queries,
            avg_precision_at_k=avg_precision_at_k,
            avg_recall_at_k=avg_recall_at_k,
            avg_f1_at_k=avg_f1_at_k,
            avg_mrr=avg_mrr,
            avg_ndcg_at_k=avg_ndcg_at_k,
            exact_match_count=exact_match_count,
            exact_match_rate=exact_match_rate,
            queries_with_results=queries_with_results,
            queries_with_results_rate=queries_with_results_rate,
            avg_k=avg_k,
            success_rate_at_k=success_rate_at_k,
            avg_query_time_ms=avg_query_time_ms,
            max_query_time_ms=max_query_time_ms,
            min_query_time_ms=min_query_time_ms,
        )

    def _create_empty_aggregated_metrics(self) -> AggregatedMetrics:
        """创建空的聚合指标"""
        return AggregatedMetrics(
            total_queries=0,
            avg_precision_at_k={k: 0.0 for k in self.k_values},
            avg_recall_at_k={k: 0.0 for k in self.k_values},
            avg_f1_at_k={k: 0.0 for k in self.k_values},
            avg_mrr=0.0,
            avg_ndcg_at_k={k: 0.0 for k in self.k_values},
            exact_match_count=0,
            exact_match_rate=0.0,
            queries_with_results=0,
            queries_with_results_rate=0.0,
            avg_k=0.0,
            success_rate_at_k={k: 0.0 for k in self.k_values},
            avg_query_time_ms=0.0,
            max_query_time_ms=0.0,
            min_query_time_ms=0.0,
        )

    def print_metrics_summary(self, metrics: AggregatedMetrics):
        """打印指标摘要"""
        logger.info("=== 评估指标摘要 ===")
        logger.info(f"总查询数: {metrics.total_queries}")
        logger.info(f"平均输出K值: {metrics.avg_k:.2f}")
        logger.info(
            f"精确匹配率: {metrics.exact_match_rate:.3f} ({metrics.exact_match_count}/{metrics.total_queries})"
        )
        logger.info(f"有结果查询率: {metrics.queries_with_results_rate:.3f}")
        logger.info(
            f"平均MRR@{max(metrics.avg_precision_at_k.keys()) if metrics.avg_precision_at_k else 'K'}: {metrics.avg_mrr:.3f}"
        )

        logger.info("\n=== 成功率@K ===")
        for k in sorted(metrics.success_rate_at_k.keys()):
            k_label = f"{k}" if k in [1, 3, 5] else f"k({k})"
            logger.info(f"Success@{k_label}: {metrics.success_rate_at_k[k]:.3f}")

        logger.info("\n=== Precision@K ===")
        for k in sorted(metrics.avg_precision_at_k.keys()):
            k_label = f"{k}" if k in [1, 3, 5] else f"k({k})"
            logger.info(f"P@{k_label}: {metrics.avg_precision_at_k[k]:.3f}")

        logger.info("\n=== Recall@K ===")
        for k in sorted(metrics.avg_recall_at_k.keys()):
            k_label = f"{k}" if k in [1, 3, 5] else f"k({k})"
            logger.info(f"R@{k_label}: {metrics.avg_recall_at_k[k]:.3f}")

        logger.info("\n=== F1@K ===")
        for k in sorted(metrics.avg_f1_at_k.keys()):
            k_label = f"{k}" if k in [1, 3, 5] else f"k({k})"
            logger.info(f"F1@{k_label}: {metrics.avg_f1_at_k[k]:.3f}")

        logger.info("\n=== nDCG@K ===")
        for k in sorted(metrics.avg_ndcg_at_k.keys()):
            k_label = f"{k}" if k in [1, 3, 5] else f"k({k})"
            logger.info(f"nDCG@{k_label}: {metrics.avg_ndcg_at_k[k]:.3f}")

        logger.info("\n=== 查询时延 ===")
        logger.info(f"平均时延: {metrics.avg_query_time_ms:.2f}ms")
        logger.info(f"最大时延: {metrics.max_query_time_ms:.2f}ms")
        logger.info(f"最小时延: {metrics.min_query_time_ms:.2f}ms")

    def print_comparison_result(self, comparison: ComparisonResult):
        """打印单个查询的路径匹配vs内容匹配对比结果"""
        logger.info("=" * 80)
        logger.info(f"查询对比分析: {comparison.query}")
        logger.info("=" * 80)
        logger.info(f"Ground Truth: {comparison.ground_truth}")
        logger.info(f"搜索结果数量: {len(comparison.predicted_files)}")

        logger.info("\n搜索结果列表:")
        for i, (file_path, score) in enumerate(
            zip(comparison.predicted_files, comparison.scores), 1
        ):
            logger.info(f"  {i}. {file_path} (分数: {score:.3f})")

        # 路径匹配结果
        path_result = comparison.path_matching
        logger.info(f"\n{'=' * 40}")
        logger.info("路径匹配结果:")
        logger.info(f"{'=' * 40}")
        logger.info(f"精确匹配: {path_result.is_exact_match}")
        logger.info(f"匹配排名: {path_result.match_rank}")
        logger.info(f"MRR: {path_result.mrr:.3f}")

        # 内容匹配结果
        content_result = comparison.content_matching
        logger.info(f"\n{'=' * 40}")
        logger.info("内容匹配结果:")
        logger.info(f"{'=' * 40}")
        logger.info(f"精确匹配: {content_result.is_exact_match}")
        logger.info(f"匹配排名: {content_result.match_rank}")
        logger.info(f"MRR: {content_result.mrr:.3f}")

        # 详细指标对比
        logger.info(f"\n{'=' * 40}")
        logger.info("详细指标对比:")
        logger.info(f"{'=' * 40}")
        logger.info(f"{'指标':<12} {'路径匹配':<12} {'内容匹配':<12} {'差异':<10}")
        logger.info("-" * 50)

        # MRR对比
        mrr_diff = content_result.mrr - path_result.mrr
        logger.info(
            f"{'MRR':<12} {path_result.mrr:<12.3f} {content_result.mrr:<12.3f} {mrr_diff:>+7.3f}"
        )

        # P@K对比
        for k in sorted(path_result.precision_at_k.keys()):
            path_pk = path_result.precision_at_k[k]
            content_pk = content_result.precision_at_k[k]
            diff = content_pk - path_pk
            logger.info(
                f"{'P@' + str(k):<12} {path_pk:<12.3f} {content_pk:<12.3f} {diff:>+7.3f}"
            )

        # nDCG@K对比
        for k in sorted(path_result.ndcg_at_k.keys()):
            path_ndcg = path_result.ndcg_at_k[k]
            content_ndcg = content_result.ndcg_at_k[k]
            diff = content_ndcg - path_ndcg
            logger.info(
                f"{'nDCG@' + str(k):<12} {path_ndcg:<12.3f} {content_ndcg:<12.3f} {diff:>+7.3f}"
            )

        # 分析结论
        logger.info(f"\n{'=' * 40}")
        logger.info("分析结论:")
        logger.info(f"{'=' * 40}")
        if content_result.mrr > path_result.mrr:
            logger.info("✅ 内容匹配表现更好")
            logger.info(
                f"   MRR提升: {((content_result.mrr - path_result.mrr) / max(path_result.mrr, 0.001) * 100):+.1f}%"
            )
        elif content_result.mrr < path_result.mrr:
            logger.info("⚠️  路径匹配表现更好")
            logger.info(
                f"   MRR下降: {((content_result.mrr - path_result.mrr) / max(path_result.mrr, 0.001) * 100):+.1f}%"
            )
        else:
            logger.info("➡️  两种匹配方式表现相同")

        if path_result.match_rank and content_result.match_rank:
            if content_result.match_rank < path_result.match_rank:
                logger.info(
                    f"   内容匹配找到更靠前的结果 (排名 {content_result.match_rank} vs {path_result.match_rank})"
                )
            elif content_result.match_rank > path_result.match_rank:
                logger.info(
                    f"   路径匹配找到更靠前的结果 (排名 {path_result.match_rank} vs {content_result.match_rank})"
                )
        elif content_result.match_rank and not path_result.match_rank:
            logger.info("   内容匹配找到了相关结果，而路径匹配没有找到")
        elif path_result.match_rank and not content_result.match_rank:
            logger.info("   路径匹配找到了相关结果，而内容匹配没有找到")


if __name__ == "__main__":
    # 测试代码
    calculator = MetricsCalculator()

    # 测试场景1：内容匹配优于路径匹配的情况
    logger.info("测试场景1：内容匹配优于路径匹配")
    ground_truth_content = """
    public getPathLength(): number{
        return this._pathLength;
    }
    """

    predicted_contents = [
        """
        public getPathLength(): number{
            return this._pathLength;
        }
        """,  # 内容正确，但路径错误
        "some other unrelated content",  # 路径正确，但内容错误
        "another unrelated content",
    ]

    comparison1 = calculator.calculate_comparison_metrics(
        query="获取路径长度",
        ground_truth_file="library/src/main/ets/batik/svggen/SVGPath.ets",
        predicted_files=[
            "wrong/path/SVGPath.ets",  # 路径错误
            "library/src/main/ets/batik/svggen/SVGPath.ets",  # 路径正确
            "other/file.ets",
        ],
        scores=[0.95, 0.8, 0.6],
        ground_truth_content=ground_truth_content,
        predicted_contents=predicted_contents,
    )

    calculator.print_comparison_result(comparison1)

    # 测试场景2：路径匹配和内容匹配结果相同的情况
    logger.info("\n\n测试场景2：路径匹配和内容匹配结果相同")
    predicted_contents2 = [
        "unrelated content 1",
        """
        public getPathLength(): number{
            return this._pathLength;
        }
        """,  # 内容和路径都正确
        "unrelated content 2",
    ]

    comparison2 = calculator.calculate_comparison_metrics(
        query="获取路径长度",
        ground_truth_file="library/src/main/ets/batik/svggen/SVGPath.ets",
        predicted_files=[
            "other1.ets",
            "library/src/main/ets/batik/svggen/SVGPath.ets",  # 路径和内容都正确
            "other2.ets",
        ],
        scores=[0.9, 0.8, 0.7],
        ground_truth_content=ground_truth_content,
        predicted_contents=predicted_contents2,
    )

    calculator.print_comparison_result(comparison2)
