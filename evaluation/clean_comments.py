import sys
import os

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
from utils.code_format import ClearCommentsForC
from utils.file_oper import read_file
import os

if __name__ == "__main__":
    repo_paths = [
        "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs_nocomments",
        "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_HarmonyOS_Codelabs_nocomments",
    ]
    for repo_path in repo_paths:
        for root, _, files in os.walk(repo_path):
            for file in files:
                if not file.endswith(".ets"):
                    continue
                file_path = os.path.join(root, file)
                content = read_file(file_path)
                content = ClearCommentsForC(content)
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
