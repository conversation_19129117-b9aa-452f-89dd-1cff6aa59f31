"""
基于内容的评估器
用于处理包含文件内容的数据集，进行更准确的评估
"""

import json
import logging
from typing import List, Dict, Any
from dataclasses import dataclass
from metrics_calculator import (
    MetricsCalculator,
    EvaluationResult,
    AggregatedMetrics,
    ComparisonResult,
)
import sys
import os

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
from utils.code_format import ClearCommentsForC

logger = logging.getLogger(__name__)


@dataclass
class QueryData:
    """查询数据结构"""

    query: str
    ground_truth_file: str
    ground_truth_content: str
    repo: str
    functions: List[Dict[str, Any]]


@dataclass
class SearchResult:
    """搜索结果数据结构"""

    file_path: str
    content: str
    score: float


class ContentBasedEvaluator:
    """基于内容的评估器"""

    def __init__(self, k_values: List[int] = None):
        """
        初始化评估器

        Args:
            k_values: 要计算的K值列表
        """
        self.calculator = MetricsCalculator(k_values)

    def load_dataset(self, dataset_path: str) -> List[QueryData]:
        """
        加载数据集

        Args:
            dataset_path: 数据集文件路径（JSONL格式）

        Returns:
            查询数据列表
        """
        queries = []

        try:
            with open(dataset_path, "r", encoding="utf-8") as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        data = json.loads(line.strip())

                        # 从functions中提取查询信息
                        if "functions" in data and data["functions"]:
                            for func in data["functions"]:
                                if "comment_zh" in func and func["comment_zh"]:
                                    query_data = QueryData(
                                        query=func["comment_zh"],
                                        ground_truth_file=data.get("filepath", ""),
                                        ground_truth_content=ClearCommentsForC(
                                            data.get("body", "")
                                        ),
                                        repo=data.get("repo", ""),
                                        functions=data.get("functions", []),
                                    )
                                    queries.append(query_data)

                    except json.JSONDecodeError as e:
                        logger.warning(f"跳过第{line_num}行，JSON解析错误: {e}")
                        continue
                    except Exception as e:
                        logger.warning(f"跳过第{line_num}行，处理错误: {e}")
                        continue

        except FileNotFoundError:
            logger.error(f"数据集文件未找到: {dataset_path}")
            return []
        except Exception as e:
            logger.error(f"加载数据集失败: {e}")
            return []

        logger.info(f"成功加载 {len(queries)} 个查询")
        return queries

    def evaluate_search_results(
        self,
        query_data: QueryData,
        search_results: List[SearchResult],
        use_content_matching: bool = True,
    ) -> EvaluationResult:
        """
        评估单个查询的搜索结果

        Args:
            query_data: 查询数据
            search_results: 搜索结果列表
            use_content_matching: 是否使用内容匹配

        Returns:
            评估结果
        """
        predicted_files = [result.file_path for result in search_results]
        scores = [result.score for result in search_results]

        if use_content_matching:
            predicted_contents = [result.content for result in search_results]
            return self.calculator.calculate_single_query_metrics(
                query=query_data.query,
                ground_truth_file=query_data.ground_truth_file,
                predicted_files=predicted_files,
                scores=scores,
                ground_truth_content=query_data.ground_truth_content,
                predicted_contents=predicted_contents,
            )
        else:
            return self.calculator.calculate_single_query_metrics(
                query=query_data.query,
                ground_truth_file=query_data.ground_truth_file,
                predicted_files=predicted_files,
                scores=scores,
            )

    def evaluate_search_results_comparison(
        self, query_data: QueryData, search_results: List[SearchResult]
    ) -> ComparisonResult:
        """
        评估单个查询的搜索结果，同时返回路径匹配和内容匹配的对比

        Args:
            query_data: 查询数据
            search_results: 搜索结果列表

        Returns:
            对比评估结果
        """
        predicted_files = [result.file_path for result in search_results]
        scores = [result.score for result in search_results]
        predicted_contents = [result.content for result in search_results]

        return self.calculator.calculate_comparison_metrics(
            query=query_data.query,
            ground_truth_file=query_data.ground_truth_file,
            predicted_files=predicted_files,
            scores=scores,
            ground_truth_content=query_data.ground_truth_content,
            predicted_contents=predicted_contents,
        )

    def batch_evaluate(
        self,
        queries: List[QueryData],
        search_function,
        use_content_matching: bool = True,
        max_results: int = 10,
    ) -> AggregatedMetrics:
        """
        批量评估

        Args:
            queries: 查询数据列表
            search_function: 搜索函数，接受查询字符串，返回SearchResult列表
            use_content_matching: 是否使用内容匹配
            max_results: 每个查询的最大结果数

        Returns:
            聚合评估指标
        """
        evaluation_results = []
        query_times = []

        for i, query_data in enumerate(queries):
            try:
                logger.info(
                    f"评估查询 {i + 1}/{len(queries)}: {query_data.query[:50]}..."
                )

                # 执行搜索
                import time

                start_time = time.time()
                search_results = search_function(query_data.query)
                end_time = time.time()

                query_time_ms = (end_time - start_time) * 1000
                query_times.append(query_time_ms)

                # 限制结果数量
                if len(search_results) > max_results:
                    search_results = search_results[:max_results]

                # 评估结果
                result = self.evaluate_search_results(
                    query_data, search_results, use_content_matching
                )
                evaluation_results.append(result)

            except Exception as e:
                logger.error(f"评估查询失败: {query_data.query[:50]}..., 错误: {e}")
                continue

        # 聚合指标
        return self.calculator.aggregate_metrics(evaluation_results, query_times)

    def compare_matching_methods(
        self, queries: List[QueryData], search_function, max_results: int = 10
    ) -> Dict[str, AggregatedMetrics]:
        """
        比较路径匹配和内容匹配的效果

        Args:
            queries: 查询数据列表
            search_function: 搜索函数
            max_results: 每个查询的最大结果数

        Returns:
            包含两种匹配方法结果的字典
        """
        logger.info("开始比较路径匹配和内容匹配...")

        # 路径匹配评估
        logger.info("执行路径匹配评估...")
        path_metrics = self.batch_evaluate(
            queries,
            search_function,
            use_content_matching=False,
            max_results=max_results,
        )

        # 内容匹配评估
        logger.info("执行内容匹配评估...")
        content_metrics = self.batch_evaluate(
            queries, search_function, use_content_matching=True, max_results=max_results
        )

        return {"path_matching": path_metrics, "content_matching": content_metrics}

    def batch_evaluate_with_comparison(
        self,
        queries: List[QueryData],
        search_function,
        max_results: int = 10,
        show_individual_results: bool = False,
    ) -> Dict[str, AggregatedMetrics]:
        """
        批量评估并显示每个查询的对比结果

        Args:
            queries: 查询数据列表
            search_function: 搜索函数
            max_results: 每个查询的最大结果数
            show_individual_results: 是否显示每个查询的详细对比结果

        Returns:
            聚合的对比结果
        """
        path_evaluation_results = []
        content_evaluation_results = []
        query_times = []

        logger.info(f"开始批量评估 {len(queries)} 个查询...")
        logger.info("=" * 80)

        for i, query_data in enumerate(queries):
            try:
                logger.info(
                    f"\n评估查询 {i + 1}/{len(queries)}: {query_data.query[:60]}..."
                )

                # 执行搜索
                import time

                start_time = time.time()
                search_results = search_function(query_data.query)
                end_time = time.time()

                query_time_ms = (end_time - start_time) * 1000
                query_times.append(query_time_ms)

                # 限制结果数量
                if len(search_results) > max_results:
                    search_results = search_results[:max_results]

                # 获取对比评估结果
                comparison = self.evaluate_search_results_comparison(
                    query_data, search_results
                )

                # 收集结果用于聚合
                path_evaluation_results.append(comparison.path_matching)
                content_evaluation_results.append(comparison.content_matching)

                # 显示个别结果（如果需要）
                if show_individual_results:
                    self.calculator.print_comparison_result(comparison)
                else:
                    # 简要显示结果
                    path_mrr = comparison.path_matching.mrr
                    content_mrr = comparison.content_matching.mrr
                    improvement = content_mrr - path_mrr
                    status = (
                        "✅" if improvement > 0 else "⚠️" if improvement < 0 else "➡️"
                    )
                    logger.info(
                        f"  {status} 路径MRR: {path_mrr:.3f}, 内容MRR: {content_mrr:.3f}, 差异: {improvement:+.3f}"
                    )

            except Exception as e:
                logger.error(f"评估查询失败: {query_data.query[:50]}..., 错误: {e}")
                continue

        # 聚合指标
        path_metrics = self.calculator.aggregate_metrics(
            path_evaluation_results, query_times
        )
        content_metrics = self.calculator.aggregate_metrics(
            content_evaluation_results, query_times
        )

        # 显示聚合结果
        logger.info("\n" + "=" * 80)
        logger.info("聚合评估结果:")
        logger.info("=" * 80)
        self.print_comparison_results(
            {"path_matching": path_metrics, "content_matching": content_metrics}
        )

        return {"path_matching": path_metrics, "content_matching": content_metrics}

    def print_comparison_results(
        self, comparison_results: Dict[str, AggregatedMetrics]
    ):
        """打印比较结果"""
        path_metrics = comparison_results["path_matching"]
        content_metrics = comparison_results["content_matching"]

        logger.info("=" * 60)
        logger.info("路径匹配 vs 内容匹配 比较结果")
        logger.info("=" * 60)

        logger.info(f"{'指标':<15} {'路径匹配':<12} {'内容匹配':<12} {'提升':<10}")
        logger.info("-" * 60)

        # 精确匹配率
        path_exact = path_metrics.exact_match_rate
        content_exact = content_metrics.exact_match_rate
        improvement = (
            ((content_exact - path_exact) / path_exact * 100) if path_exact > 0 else 0
        )
        logger.info(
            f"{'精确匹配率':<15} {path_exact:<12.3f} {content_exact:<12.3f} {improvement:>+7.1f}%"
        )

        # MRR
        path_mrr = path_metrics.avg_mrr
        content_mrr = content_metrics.avg_mrr
        improvement = ((content_mrr - path_mrr) / path_mrr * 100) if path_mrr > 0 else 0
        logger.info(
            f"{'平均MRR':<15} {path_mrr:<12.3f} {content_mrr:<12.3f} {improvement:>+7.1f}%"
        )

        # P@K指标
        for k in sorted(path_metrics.avg_precision_at_k.keys()):
            path_pk = path_metrics.avg_precision_at_k[k]
            content_pk = content_metrics.avg_precision_at_k[k]
            improvement = ((content_pk - path_pk) / path_pk * 100) if path_pk > 0 else 0
            logger.info(
                f"{'P@' + str(k):<15} {path_pk:<12.3f} {content_pk:<12.3f} {improvement:>+7.1f}%"
            )

        # nDCG@K指标
        for k in sorted(path_metrics.avg_ndcg_at_k.keys()):
            path_ndcg = path_metrics.avg_ndcg_at_k[k]
            content_ndcg = content_metrics.avg_ndcg_at_k[k]
            improvement = (
                ((content_ndcg - path_ndcg) / path_ndcg * 100) if path_ndcg > 0 else 0
            )
            logger.info(
                f"{'nDCG@' + str(k):<15} {path_ndcg:<12.3f} {content_ndcg:<12.3f} {improvement:>+7.1f}%"
            )


if __name__ == "__main__":
    # 示例用法
    evaluator = ContentBasedEvaluator()

    # 模拟搜索函数 - 创建更真实的测试场景
    def mock_search_function(query: str) -> List[SearchResult]:
        """模拟搜索函数，根据查询返回不同的结果"""
        if "获取路径长度" in query:
            return [
                SearchResult(
                    "wrong/path/SVGPath.ets",
                    "public getPathLength(): number{ return this._pathLength; }",
                    0.95,
                ),
                SearchResult(
                    "SVGPath.ets",
                    "public getWidth(): number{ return this._width; }",
                    0.85,
                ),
                SearchResult("other.ets", "unrelated content", 0.75),
            ]
        else:
            return [
                SearchResult("file1.ets", "some content", 0.9),
                SearchResult("file2.ets", "other content", 0.8),
                SearchResult("file3.ets", "another content", 0.7),
            ]

    # 创建测试查询
    test_queries = [
        QueryData(
            query="获取路径长度",
            ground_truth_file="SVGPath.ets",
            ground_truth_content="public getPathLength(): number{ return this._pathLength; }",
            repo="test_repo",
            functions=[],
        ),
        QueryData(
            query="设置宽度",
            ground_truth_file="SVGRect.ets",
            ground_truth_content="public setWidth(width: number): void{ this._width = width; }",
            repo="test_repo",
            functions=[],
        ),
    ]

    logger.info("=== 使用新的批量对比评估功能 ===")
    # 使用新的批量对比评估功能
    results = evaluator.batch_evaluate_with_comparison(
        test_queries,
        mock_search_function,
        show_individual_results=True,  # 显示每个查询的详细结果
    )
