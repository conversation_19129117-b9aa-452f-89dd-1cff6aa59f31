{"content": "/**\n * Copyright (C) 2022 Huawei Device Co., Ltd.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {PathOrders} from \"../util/ObjOrArrayUtil\"\n\nexport class SVGPath {\n  private _pathLength: number = 0;\n  private _d: string[] = new Array();\n  private _pathResultObj: Record<string,string|number|string[]> = {  };\n\n  /**\n   * 获取路径长度\n   */\n  public getPathLength(): number{\n    return this._pathLength;\n  }\n\n  /**\n   * 设置路径长度\n   * @param newPathLength 路径长度\n   */\n  public setPathLength(newPathLength: number): void{\n    this._pathLength = newPathLength;\n    this._pathResultObj['pathLength'] = newPathLength;\n  }\n\n  /**\n   * 获取路径长度\n   */\n  public getD(): string[]{\n    return this._d;\n  }\n\n  /**\n   * 设置路径长度\n   * @param newPathLength 路径长度\n   */\n  public setD(newD: string[]): void{\n    this._d = newD;\n    this._pathResultObj['d'] = newD;\n  }\n\n  /**\n   * 添加顶点\n   * @param x 顶点的X坐标\n   * @param y 顶点的Y坐标\n   */\n  public addPoints(order: string, x?: number, y?: number): void{\n    let index = PathOrders.indexOf(order);\n    if (index === -1) {\n      return;\n    }\n\n    let point = '';\n    if (order === 'z' || order === 'Z') {\n      point = order = ' ';\n    } else if (x === undefined) {\n      x = 0;\n    } else if (y === undefined) {\n      y = 0;\n    }\n\n    if (order && x !== undefined && y !== undefined) {\n      point = order + ' ' + x + ',' + y + ' ';\n    }\n    this._d.push(point);\n  }\n\n  /**\n   * 添加不带命令的坐标点\n   * @param x 要运动到的点的x 坐标\n   * @param y 要运动到的点的y 坐标\n   */\n  public addPointsWithoutOrder(x: number, y: number): void{\n    if (x === undefined) {\n      x = 0;\n    }\n    if (y === undefined) {\n      y = 0;\n    }\n    this._d.push(x + ',' + y);\n  }\n\n  public addAttribute(key: string, value: string): void{\n    this._pathResultObj[key] = value;\n  }\n\n  public toObj(): object{\n    let dWithoutComma = this._d.join(' ');\n    this._pathResultObj['d'] = dWithoutComma;\n    return this._pathResultObj;\n  }\n}\n", "filepath": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs_nocomments/XmlGraphicsBatik/library/src/main/ets/batik/svggen/SVGPath.ets", "repo": "XmlGraphicsBatik", "isArkUI": false, "functions": [{"name": "getPathLength", "comment": "* 获取路径长度", "body": "public getPathLength(): number{\n    return this._pathLength;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 3, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 3.75, "Relevance": 4}, "comment_zh": "获取路径长度"}], "token_count": 726, "content_with_no_comment": "\nimport {PathOrders} from \"../util/ObjOrArrayUtil\"\n\nexport class SVGPath {\n  private _pathLength: number = 0;\n  private _d: string[] = new Array();\n  private _pathResultObj: Record<string,string|number|string[]> = {  };\n\n  \n  public getPathLength(): number{\n    return this._pathLength;\n  }\n\n  \n  public setPathLength(newPathLength: number): void{\n    this._pathLength = newPathLength;\n    this._pathResultObj['pathLength'] = newPathLength;\n  }\n\n  \n  public getD(): string[]{\n    return this._d;\n  }\n\n  \n  public setD(newD: string[]): void{\n    this._d = newD;\n    this._pathResultObj['d'] = newD;\n  }\n\n  \n  public addPoints(order: string, x?: number, y?: number): void{\n    let index = PathOrders.indexOf(order);\n    if (index === -1) {\n      return;\n    }\n\n    let point = '';\n    if (order === 'z' || order === 'Z') {\n      point = order = ' ';\n    } else if (x === undefined) {\n      x = 0;\n    } else if (y === undefined) {\n      y = 0;\n    }\n\n    if (order && x !== undefined && y !== undefined) {\n      point = order + ' ' + x + ',' + y + ' ';\n    }\n    this._d.push(point);\n  }\n\n  \n  public addPointsWithoutOrder(x: number, y: number): void{\n    if (x === undefined) {\n      x = 0;\n    }\n    if (y === undefined) {\n      y = 0;\n    }\n    this._d.push(x + ',' + y);\n  }\n\n  public addAttribute(key: string, value: string): void{\n    this._pathResultObj[key] = value;\n  }\n\n  public toObj(): object{\n    let dWithoutComma = this._d.join(' ');\n    this._pathResultObj['d'] = dWithoutComma;\n    return this._pathResultObj;\n  }\n}\n"}
{"content": "/*\n * Copyright (C) 2022 Huawei Device Co., Ltd.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport Matrix4 from '@ohos.matrix4'\nimport ImageViewState from './ImageViewState'\nimport display from '@ohos.display';\n\n\nexport interface OnLongPressListener {\n  onLongPress: (event: GestureEvent) => void;\n}\n\nexport interface OnDoubleTapListener {\n  onDoubleTap: (event: GestureEvent) => void;\n}\n\nexport interface OnSingleTapListener {\n  onSingleTapConfirmed: (event: ClickEvent) => void;\n}\n\nexport interface OnStateChangedListener {\n  onScaleChanged(newScale: number): void;\n}\n\nexport interface OnImageEventListener {\n\n  onImageLoaded(): void;\n\n  onImageLoadError(): void;\n}\n\nexport enum Config {\n  ALPHA_8,\n  RGB_565,\n  ARGB_8888,\n  RGBA_F16,\n  HARDWARE\n}\n\ninterface ImageOnComplete {\n  width: number;\n  height: number;\n  componentWidth: number;\n  componentHeight: number;\n  loadingStatus: number;\n  contentWidth: number;\n  contentHeight: number;\n  contentOffsetX: number;\n  contentOffsetY: number;\n}\n\n@ComponentV2\nstruct SubsamplingScaleImageView {\n  @Param model: SubsamplingScaleImageView.Model = new SubsamplingScaleImageView.Model()\n\n  build() {\n    Flex({ direction: FlexDirection.Column, alignItems: ItemAlign.Center, justifyContent: FlexAlign.Center }) {\n      Image(this.model.src)\n        .draggable(false)\n        .alt(this.model.previewSource)\n        .objectFit(ImageFit.Contain)\n        .transform(this.model.matrix)\n        .onComplete((msg?: ImageOnComplete) => {\n          if (!!msg) {\n            this.model.sWidth = msg.width;\n            this.model.sHeight = msg.height;\n            this.model.componentWidth = msg.componentWidth;\n            this.model.componentHeight = msg.componentHeight;\n            this.model.readySent = true;\n            this.model.imageLoadedSent = true;\n            if (this.model.onImageEventListener != null) {\n              this.model.onImageEventListener.onImageLoaded();\n            }\n          }\n        })\n        .onError(() => {\n          this.model.readySent = false;\n          if (this.model.onImageEventListener != null) {\n            this.model.onImageEventListener.onImageLoadError();\n          }\n        })\n\n    }\n    .priorityGesture(\n      TapGesture({ count: 2, fingers: 1 })\n        .onAction((event:GestureEvent) => {\n          if(!event){\n            return\n          }\n          if (this.model.zoomEnabled) {\n            if (this.model.scale != this.model.getMinScale()) {\n              this.model.scale = this.model.getMinScale();\n              this.model.resetScaleAndCenter();\n              this.model.isZooming = false;\n            } else {\n              this.model.isZooming = true;\n              if (this.model.quickScaleEnabled) {\n                this.zoomTo(this.model.maxScale, this.model.doubleTapZoomDuration);\n              } else {\n                let fingerInfo: FingerInfo[] = event.fingerList;\n                if (!!fingerInfo && fingerInfo.length > 0) {\n                  this.model.setDoubleScale(fingerInfo[0].localX, fingerInfo[0].localY);\n                }\n              }\n            }\n            if (this.model.onStateChangedListener != null) {\n              this.model.onStateChangedListener.onScaleChanged(this.model.scale);\n            }\n          } else {\n            if (!!event) {\n              if (this.model.doubleTapListener != null) {\n                this.model.doubleTapListener.onDoubleTap(event);\n              }\n            }\n          }\n        })\n    )\n    .gesture(\n      GestureGroup(GestureMode.Parallel,\n        LongPressGesture({ repeat: true })\n          .onAction((event:GestureEvent) => {\n            if (!!event && this.model.longPressListener != null)  {\n                this.model.longPressListener.onLongPress(event);\n              }\n          })\n          .onActionEnd(() => {\n          }),\n        PanGesture({})\n          .onActionStart((event:GestureEvent) => {\n          })\n          .onActionUpdate((event:GestureEvent) => {\n            if (!!event) {\n\n              if (this.model.canScroll() && this.model.panEnabled) {\n\n                let distanceX: number = this.model.startOffsetX + event.offsetX;\n                let distanceY: number = this.model.startOffsetY + event.offsetY;\n                if (this.model.panLimit == this.model.PAN_LIMIT_INSIDE) {\n                } else if (this.model.panLimit == this.model.PAN_LIMIT_OUTSIDE) {\n\n                } else {\n                }\n                this.model.offsetX = distanceX;\n                this.model.offsetY = distanceY;\n                this.model.updateMatrix()\n              }\n\n            }\n          })\n          .onActionEnd((event) => {\n            if (!!event) {\n              this.model.startOffsetX = this.model.startOffsetX + event.offsetX\n              this.model.startOffsetY = this.model.startOffsetY + event.offsetY\n\n            }\n          }),\n        PinchGesture({ fingers: 2 })\n          .onActionStart((event) => {\n            this.model.baseScale = this.model.scale\n          })\n          .onActionUpdate((event) => {\n            if (!!event) {\n              if (!this.model.zoomEnabled) return;\n              if (this.model.quickScaleEnabled) {\n                this.zoomTo(event.scale, 0);\n              } else {\n                let currentScale: number = this.model.baseScale * event.scale;\n                if (currentScale > this.model.getMaxScale()) {\n                  this.model.scale = this.model.getMaxScale()\n                } else if (currentScale < this.model.getMinScale()) {\n                  this.model.scale = this.model.getMinScale()\n                } else {\n                  this.model.scale = currentScale;\n                }\n                console.info(\"Subsampling pin:\" + this.model.scale)\n                this.model.updateMatrix()\n                //              this.zoomTo(event.scale, this.doubleTapZoomDuration);\n              }\n              this.model.isZooming = this.model.canScroll();\n            }\n          })\n          .onActionEnd((event) => {\n            this.model.baseScale = this.model.scale;\n            if (this.model.onStateChangedListener != null) {\n              this.model.onStateChangedListener.onScaleChanged(this.model.scale);\n            }\n          })\n      ).onCancel(() => {\n        console.log('Parallel gesture canceled')\n      })\n\n    )\n    .onClick((event) => {\n      if (this.model.singleTapListener != null && !!event) {\n        this.model.singleTapListener.onSingleTapConfirmed(event);\n      }\n    })\n    .backgroundColor(this.model.tileBgColor)\n  }\n\n  public zoomTo(scale: number, durationMs: number): void {\n    let currentScale = 0;\n    if (scale > this.model.getMaxScale()) {\n      currentScale = this.model.getMaxScale()\n    } else if (scale < this.model.getMinScale()) {\n      currentScale = this.model.getMinScale()\n    } else {\n      currentScale = scale;\n    }\n    animateTo({\n      duration: durationMs,\n      tempo: 0.5,\n      curve: Curve.EaseInOut,\n      delay: 0,\n      iterations: 1,\n      playMode: PlayMode.Normal,\n      onFinish: () => {\n      }\n    }, () => {\n      this.model.scale = currentScale;\n      this.model.updateMatrix();\n    })\n  }\n}\n\n namespace SubsamplingScaleImageView {\n  @ObservedV2\n  export class Model {\n    @Trace src: string | PixelMap | Resource = '';\n    @Trace previewSource: string | Resource = '';\n    @Trace sWidth: number = 0;\n    @Trace sHeight: number = 0;\n    @Trace componentWidth: number = 0;\n    @Trace componentHeight: number = 0;\n    @Trace scale: number = 1;\n    @Trace baseScale: number = 1;\n    @Trace touchCenterX: number = 0;\n    @Trace touchCenterY: number = 0;\n    @Trace offsetX: number = 0;\n    @Trace offsetY: number = 0;\n    @Trace startOffsetX: number = 0;\n    @Trace startOffsetY: number = 0;\n    @Trace maxScale: number = 4;\n    @Trace doubleTapZoomScale: number = 1;\n    @Trace minScale: number = 1;\n    @Trace quickScaleEnabled: boolean = false;\n    @Trace panEnabled: boolean = true;\n    @Trace zoomEnabled: boolean = true;\n    @Trace doubleEnabled: boolean = true;\n    @Trace orientation: number = 0;\n    @Trace tileBgColor: number = 0x000000;\n    @Trace readySent: boolean = false;\n    @Trace doubleTapZoomDuration: number = 0;\n    @Trace imageLoadedSent: boolean = false;\n    @Trace doubleTapListener: OnDoubleTapListener | null = null;\n    @Trace singleTapListener: OnSingleTapListener | null = null;\n    @Trace longPressListener: OnLongPressListener | null = null;\n    @Trace onImageEventListener: OnImageEventListener | null = null;\n    @Trace onStateChangedListener: OnStateChangedListener | null = null;\n    @Trace preferredBitmapConfig: Config = Config.ALPHA_8;\n    @Trace isZooming: boolean = false;\n    @Trace /** Don't allow the image to be panned off screen. As much of the image as possible is always displayed, centered in the view when it is smaller. This is the best option for galleries. */\n    @Trace PAN_LIMIT_INSIDE: number = 1;\n    @Trace /** Allows the image to be panned until it is just off screen, but no further. The edge of the image will stop when it is flush with the screen edge. */\n    @Trace PAN_LIMIT_OUTSIDE: number = 2;\n    @Trace /** Allows the image to be panned until a corner reaches the center of the screen but no further. Useful when you want to pan any spot on the image to the exact center of the screen. */\n    @Trace PAN_LIMIT_CENTER: number = 3;\n    @Trace panLimit: number = 1;\n    @Trace scaledDensity: number = 0;\n    @Trace matrix: object = Matrix4.identity()\n      .rotate({ x: 0, y: 0, z: 1, angle: this.orientation })\n      .translate({ x: 0, y: 0 })\n      .scale({ x: this.scale, y: this.scale, centerX: this.touchCenterX, centerY: this.touchCenterY });\n\n    public setImage(src: string | PixelMap | Resource, previewSource?: string | Resource, state?: ImageViewState): Model {\n      this.src = src;\n      if (!!previewSource) {\n        this.previewSource = previewSource;\n      }\n      if (state != null) {\n        this.scale = state.getScale();\n        this.orientation = state.getOrientation();\n        this.touchCenterX = state.getCenterX();\n        this.touchCenterY = state.getCenterY();\n      }\n      this.setScaleDensity();\n      return this;\n    }\n\n\n    private setScaleDensity() {\n      try {\n        this.scaledDensity = display.getDefaultDisplaySync().scaledDensity;\n      } catch (e) {\n        console.error(\"SubsamplingScaleImageView setScaledDensity error\")\n      }\n\n    }\n\n    public setScale(scale: number): Model {\n      this.scale = scale;\n      return this;\n    }\n\n    public getScale(): number {\n      return this.scale;\n    }\n\n    public setMaxScale(maxScale: number): Model {\n      this.maxScale = maxScale;\n      return this;\n    }\n\n    public setMinScale(minScale: number): Model {\n      this.minScale = minScale;\n      return this;\n    }\n\n    public setQuickScaleEnabled(quickScaleEnabled: boolean): Model {\n      this.quickScaleEnabled = quickScaleEnabled;\n      return this;\n    }\n\n    public isPanEnabled(): boolean {\n      return this.panEnabled;\n    }\n\n    public setPanEnabled(panEnabled: boolean): Model {\n      this.panEnabled = panEnabled;\n      return this;\n    }\n\n    public isZoomEnabled(): boolean {\n      return this.zoomEnabled;\n    }\n\n    public setDoubleZoom(doubleEnabled: boolean): Model {\n      this.doubleEnabled = doubleEnabled;\n      return this;\n    }\n\n    public setZoomEnabled(zoomEnabled: boolean): Model {\n      this.zoomEnabled = zoomEnabled;\n      return this;\n    }\n\n    public setTileBackgroundColor(tileBgColor: number): Model {\n      this.tileBgColor = tileBgColor;\n      return this;\n    }\n\n    public getSWidth(): number {\n      return this.sWidth;\n    }\n\n    public getSHeight(): number {\n      return this.sHeight;\n    }\n\n    public canScroll(): boolean {\n      if (this.zoomEnabled && this.getScale() > 1) {\n        return true;\n      } else {\n        return false;\n      }\n    }\n\n    public setOrientation(degrees: number): Model {\n      this.orientation = degrees;\n      this.updateMatrix();\n      return this;\n    }\n\n    public setDoubleTapListener(listener: OnDoubleTapListener): Model {\n      this.doubleTapListener = listener;\n      return this;\n    }\n\n    public setSingleTapListener(listener: OnSingleTapListener): Model {\n      this.singleTapListener = listener;\n      return this;\n    }\n\n    public setLongPressListener(listener: OnLongPressListener): Model {\n      this.longPressListener = listener;\n      return this;\n    }\n\n    /**\n     * Add a listener for pan and zoom events. Extend {@link DefaultOnStateChangedListener} to simplify\n     * implementation.\n     * @param onStateChangedListener an {@link OnStateChangedListener} instance.\n     */\n    public setOnStateChangedListener(onStateChangedListener: OnStateChangedListener): Model {\n      this.onStateChangedListener = onStateChangedListener;\n      return this;\n    }\n\n    public setOnImageEventListener(onImageEventListener: OnImageEventListener): Model {\n      this.onImageEventListener = onImageEventListener;\n      return this;\n    }\n\n    public getMaxScale(): number {\n      return this.maxScale;\n    }\n\n    public getMinScale(): number {\n      return this.minScale;\n    }\n\n    public setDoubleScale(localX: number, localY: number): void {\n      let distanceCenterX = this.componentWidth / 2 - vp2px(localX) + this.offsetX;\n      let distanceCenterY = this.componentHeight / 2 - vp2px(localY) + this.offsetY\n      this.scale = this.getMaxScale();\n      this.touchCenterX = 10\n      this.touchCenterY = 10\n      this.startOffsetX = distanceCenterX\n      this.startOffsetY = distanceCenterY\n      this.setOffset(distanceCenterX, distanceCenterY)\n    }\n\n    public setOffset(offsetX: number, offsetY: number): void {\n      this.offsetX = offsetX;\n      this.offsetY = offsetY;\n\n      this.updateMatrix();\n    }\n\n    public updateMatrix(): void {\n      this.matrix = Matrix4.identity()\n        .rotate({ x: 0, y: 0, z: 1, angle: this.orientation })\n        .translate({ x: this.offsetX, y: this.offsetY })\n        .scale({ x: this.scale, y: this.scale, centerX: this.touchCenterX, centerY: this.touchCenterY })\n    }\n\n    public resetScaleAndCenter(): void {\n      this.scale = 1;\n      this.baseScale = 1;\n      this.offsetX = 0;\n      this.offsetY = 0;\n      this.startOffsetX = 0;\n      this.startOffsetY = 0;\n      this.touchCenterX = 0;\n      this.touchCenterY = 0;\n      this.orientation = 0;\n      this.updateMatrix();\n    }\n\n    public viewToSourceX(vx: number): number {\n      return (vx - this.offsetX) * this.scaledDensity / this.scale;\n    }\n\n    /**\n     * Convert screen to source y coordinate.\n     */\n    public viewToSourceY(vy: number): number {\n      return (vy - this.offsetY) * this.scaledDensity / this.scale;\n    }\n\n    public setDoubleTapZoomScale(doubleTapZoomScale: number): Model {\n      this.doubleTapZoomScale = doubleTapZoomScale;\n      return this;\n    }\n\n    /**\n     * Call to find whether the view is initialised, has dimensions, and will display an image on\n     * the next draw. If a preview has been provided, it may be the preview that will be displayed\n     * and the full size image may still be loading. If no preview was provided, this is called once\n     * the base layer tiles of the full size image are loaded.\n     * @return true if the view is ready to display an image and accept touch gestures.\n     */\n    public isReady(): boolean {\n      return this.readySent;\n    }\n\n    public setPreferredBitmapConfig(preferredBitmapConfig: Config): Model {\n      this.preferredBitmapConfig = preferredBitmapConfig;\n      return this;\n    }\n\n    public getPreferredBitmapConfig(): Config {\n      return this.preferredBitmapConfig;\n    }\n\n    /**\n     * Set the duration of the double tap zoom animation.\n     * @param durationMs Duration in milliseconds.\n     */\n    public setDoubleTapZoomDuration(durationMs: number): Model {\n      this.doubleTapZoomDuration = Math.max(0, durationMs);\n      return this;\n    }\n\n    /**\n     * Call to find whether the main image (base layer tiles where relevant) have been loaded. Before\n     * this event the view is blank unless a preview was provided.\n     * @return true if the main image (not the preview) has been loaded and is ready to display.\n     */\n    public isImageLoaded(): boolean {\n      return this.imageLoadedSent;\n    }\n\n    /**\n     * This is a screen density aware alternative to {@link #setMaxScale(float)}; it allows you to express the maximum\n     * allowed scale in terms of the minimum pixel density. This avoids the problem of 1:1 scale still being\n     * too small on a high density screen. A sensible starting point is 160 - the default used by this view.\n     * @param dpi Source image pixel density at maximum zoom.\n     */\n    public setMinimumDpi(dpi: number): Model {\n      let screen = display.getDefaultDisplaySync()\n      let averageDpi: number = (screen.xDPI + screen.yDPI) / 2\n      this.setMaxScale(averageDpi / dpi);\n      return this;\n    }\n\n    /**\n     * This is a screen density aware alternative to {@link #setMinScale(float)}; it allows you to express the minimum\n     * allowed scale in terms of the maximum pixel density.\n     * @param dpi Source image pixel density at minimum zoom.\n     */\n    public setMaximumDpi(dpi: number): Model {\n      let screen = display.getDefaultDisplaySync()\n      let averageDpi: number = (screen.xDPI + screen.yDPI) / 2\n      this.setMinScale(averageDpi / dpi);\n      return this;\n    }\n\n    public getCenterX(): number {\n      return this.viewToSourceX(this.componentWidth / 2);\n    }\n\n    public getCenterY(): number {\n      return this.viewToSourceY(this.componentHeight / 2);\n    }\n\n    public getOrientation(): number {\n      return this.orientation;\n    }\n\n    public getState(): ImageViewState | null {\n      if (this.sWidth > 0 && this.sHeight > 0) {\n        //noinspection ConstantConditions\n        return new ImageViewState(this.getScale(), this.getCenterX(), this.getCenterY(), this.getOrientation());\n      }\n      return new ImageViewState(0, 0, 0, 0);\n    }\n\n    /**\n     * Set the pan limiting style. See static fields. Normally {@link #PAN_LIMIT_INSIDE} is best, for image galleries.\n     * @param panLimit a pan limit constant. See static fields.\n     */\n    public setPanLimit(panLimit: number): Model {\n      if (panLimit != this.PAN_LIMIT_INSIDE || panLimit != this.PAN_LIMIT_OUTSIDE || panLimit != this.PAN_LIMIT_CENTER) {\n        console.error(\"Invalid pan limit: \" + panLimit)\n      }\n      this.panLimit = panLimit;\n      return this;\n    }\n  }\n}\nexport { SubsamplingScaleImageView };", "filepath": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs_nocomments/ohos_subsampling_scale_image_view/library/src/main/ets/components/MainPage/SubsamplingScaleImageView.ets", "repo": "ohos_subsampling_scale_image_view", "isArkUI": true, "functions": [{"name": "viewToSourceY", "comment": "* Convert screen to source y coordinate.", "body": "public viewToSourceY(vy: number): number {\n      return (vy - this.offsetY) * this.scaledDensity / this.scale;\n    }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 2, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 3, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 3.25, "Relevance": 4}, "comment_zh": "将屏幕坐标转换为源y坐标。"}, {"name": "setMaximumDpi", "comment": "* This is a screen density aware alternative to {@link #setMinScale(float)}; it allows you to express the minimum\n     * allowed scale in terms of the maximum pixel density.\n     * @param dpi Source image pixel density at minimum zoom.", "body": "public setMaximumDpi(dpi: number): Model {\n      let screen = display.getDefaultDisplaySync()\n      let averageDpi: number = (screen.xDPI + screen.yDPI) / 2\n      this.setMinScale(averageDpi / dpi);\n      return this;\n    }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 3, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 2, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 3, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 3.75, "Relevance": 3.25}, "comment_zh": "这是针对屏幕密度感知的{@link #setMinScale(float)}替代方案，允许以最大像素密度表示允许的最小缩放比例。"}], "token_count": 4365, "content_with_no_comment": "\n\nimport Matrix4 from '@ohos.matrix4'\nimport ImageViewState from './ImageViewState'\nimport display from '@ohos.display';\n\n\nexport interface OnLongPressListener {\n  onLongPress: (event: GestureEvent) => void;\n}\n\nexport interface OnDoubleTapListener {\n  onDoubleTap: (event: GestureEvent) => void;\n}\n\nexport interface OnSingleTapListener {\n  onSingleTapConfirmed: (event: ClickEvent) => void;\n}\n\nexport interface OnStateChangedListener {\n  onScaleChanged(newScale: number): void;\n}\n\nexport interface OnImageEventListener {\n\n  onImageLoaded(): void;\n\n  onImageLoadError(): void;\n}\n\nexport enum Config {\n  ALPHA_8,\n  RGB_565,\n  ARGB_8888,\n  RGBA_F16,\n  HARDWARE\n}\n\ninterface ImageOnComplete {\n  width: number;\n  height: number;\n  componentWidth: number;\n  componentHeight: number;\n  loadingStatus: number;\n  contentWidth: number;\n  contentHeight: number;\n  contentOffsetX: number;\n  contentOffsetY: number;\n}\n\n@ComponentV2\nstruct SubsamplingScaleImageView {\n  @Param model: SubsamplingScaleImageView.Model = new SubsamplingScaleImageView.Model()\n\n  build() {\n    Flex({ direction: FlexDirection.Column, alignItems: ItemAlign.Center, justifyContent: FlexAlign.Center }) {\n      Image(this.model.src)\n        .draggable(false)\n        .alt(this.model.previewSource)\n        .objectFit(ImageFit.Contain)\n        .transform(this.model.matrix)\n        .onComplete((msg?: ImageOnComplete) => {\n          if (!!msg) {\n            this.model.sWidth = msg.width;\n            this.model.sHeight = msg.height;\n            this.model.componentWidth = msg.componentWidth;\n            this.model.componentHeight = msg.componentHeight;\n            this.model.readySent = true;\n            this.model.imageLoadedSent = true;\n            if (this.model.onImageEventListener != null) {\n              this.model.onImageEventListener.onImageLoaded();\n            }\n          }\n        })\n        .onError(() => {\n          this.model.readySent = false;\n          if (this.model.onImageEventListener != null) {\n            this.model.onImageEventListener.onImageLoadError();\n          }\n        })\n\n    }\n    .priorityGesture(\n      TapGesture({ count: 2, fingers: 1 })\n        .onAction((event:GestureEvent) => {\n          if(!event){\n            return\n          }\n          if (this.model.zoomEnabled) {\n            if (this.model.scale != this.model.getMinScale()) {\n              this.model.scale = this.model.getMinScale();\n              this.model.resetScaleAndCenter();\n              this.model.isZooming = false;\n            } else {\n              this.model.isZooming = true;\n              if (this.model.quickScaleEnabled) {\n                this.zoomTo(this.model.maxScale, this.model.doubleTapZoomDuration);\n              } else {\n                let fingerInfo: FingerInfo[] = event.fingerList;\n                if (!!fingerInfo && fingerInfo.length > 0) {\n                  this.model.setDoubleScale(fingerInfo[0].localX, fingerInfo[0].localY);\n                }\n              }\n            }\n            if (this.model.onStateChangedListener != null) {\n              this.model.onStateChangedListener.onScaleChanged(this.model.scale);\n            }\n          } else {\n            if (!!event) {\n              if (this.model.doubleTapListener != null) {\n                this.model.doubleTapListener.onDoubleTap(event);\n              }\n            }\n          }\n        })\n    )\n    .gesture(\n      GestureGroup(GestureMode.Parallel,\n        LongPressGesture({ repeat: true })\n          .onAction((event:GestureEvent) => {\n            if (!!event && this.model.longPressListener != null)  {\n                this.model.longPressListener.onLongPress(event);\n              }\n          })\n          .onActionEnd(() => {\n          }),\n        PanGesture({})\n          .onActionStart((event:GestureEvent) => {\n          })\n          .onActionUpdate((event:GestureEvent) => {\n            if (!!event) {\n\n              if (this.model.canScroll() && this.model.panEnabled) {\n\n                let distanceX: number = this.model.startOffsetX + event.offsetX;\n                let distanceY: number = this.model.startOffsetY + event.offsetY;\n                if (this.model.panLimit == this.model.PAN_LIMIT_INSIDE) {\n                } else if (this.model.panLimit == this.model.PAN_LIMIT_OUTSIDE) {\n\n                } else {\n                }\n                this.model.offsetX = distanceX;\n                this.model.offsetY = distanceY;\n                this.model.updateMatrix()\n              }\n\n            }\n          })\n          .onActionEnd((event) => {\n            if (!!event) {\n              this.model.startOffsetX = this.model.startOffsetX + event.offsetX\n              this.model.startOffsetY = this.model.startOffsetY + event.offsetY\n\n            }\n          }),\n        PinchGesture({ fingers: 2 })\n          .onActionStart((event) => {\n            this.model.baseScale = this.model.scale\n          })\n          .onActionUpdate((event) => {\n            if (!!event) {\n              if (!this.model.zoomEnabled) return;\n              if (this.model.quickScaleEnabled) {\n                this.zoomTo(event.scale, 0);\n              } else {\n                let currentScale: number = this.model.baseScale * event.scale;\n                if (currentScale > this.model.getMaxScale()) {\n                  this.model.scale = this.model.getMaxScale()\n                } else if (currentScale < this.model.getMinScale()) {\n                  this.model.scale = this.model.getMinScale()\n                } else {\n                  this.model.scale = currentScale;\n                }\n                console.info(\"Subsampling pin:\" + this.model.scale)\n                this.model.updateMatrix()\n                \n              }\n              this.model.isZooming = this.model.canScroll();\n            }\n          })\n          .onActionEnd((event) => {\n            this.model.baseScale = this.model.scale;\n            if (this.model.onStateChangedListener != null) {\n              this.model.onStateChangedListener.onScaleChanged(this.model.scale);\n            }\n          })\n      ).onCancel(() => {\n        console.log('Parallel gesture canceled')\n      })\n\n    )\n    .onClick((event) => {\n      if (this.model.singleTapListener != null && !!event) {\n        this.model.singleTapListener.onSingleTapConfirmed(event);\n      }\n    })\n    .backgroundColor(this.model.tileBgColor)\n  }\n\n  public zoomTo(scale: number, durationMs: number): void {\n    let currentScale = 0;\n    if (scale > this.model.getMaxScale()) {\n      currentScale = this.model.getMaxScale()\n    } else if (scale < this.model.getMinScale()) {\n      currentScale = this.model.getMinScale()\n    } else {\n      currentScale = scale;\n    }\n    animateTo({\n      duration: durationMs,\n      tempo: 0.5,\n      curve: Curve.EaseInOut,\n      delay: 0,\n      iterations: 1,\n      playMode: PlayMode.Normal,\n      onFinish: () => {\n      }\n    }, () => {\n      this.model.scale = currentScale;\n      this.model.updateMatrix();\n    })\n  }\n}\n\n namespace SubsamplingScaleImageView {\n  @ObservedV2\n  export class Model {\n    @Trace src: string | PixelMap | Resource = '';\n    @Trace previewSource: string | Resource = '';\n    @Trace sWidth: number = 0;\n    @Trace sHeight: number = 0;\n    @Trace componentWidth: number = 0;\n    @Trace componentHeight: number = 0;\n    @Trace scale: number = 1;\n    @Trace baseScale: number = 1;\n    @Trace touchCenterX: number = 0;\n    @Trace touchCenterY: number = 0;\n    @Trace offsetX: number = 0;\n    @Trace offsetY: number = 0;\n    @Trace startOffsetX: number = 0;\n    @Trace startOffsetY: number = 0;\n    @Trace maxScale: number = 4;\n    @Trace doubleTapZoomScale: number = 1;\n    @Trace minScale: number = 1;\n    @Trace quickScaleEnabled: boolean = false;\n    @Trace panEnabled: boolean = true;\n    @Trace zoomEnabled: boolean = true;\n    @Trace doubleEnabled: boolean = true;\n    @Trace orientation: number = 0;\n    @Trace tileBgColor: number = 0x000000;\n    @Trace readySent: boolean = false;\n    @Trace doubleTapZoomDuration: number = 0;\n    @Trace imageLoadedSent: boolean = false;\n    @Trace doubleTapListener: OnDoubleTapListener | null = null;\n    @Trace singleTapListener: OnSingleTapListener | null = null;\n    @Trace longPressListener: OnLongPressListener | null = null;\n    @Trace onImageEventListener: OnImageEventListener | null = null;\n    @Trace onStateChangedListener: OnStateChangedListener | null = null;\n    @Trace preferredBitmapConfig: Config = Config.ALPHA_8;\n    @Trace isZooming: boolean = false;\n    @Trace \n    @Trace PAN_LIMIT_INSIDE: number = 1;\n    @Trace \n    @Trace PAN_LIMIT_OUTSIDE: number = 2;\n    @Trace \n    @Trace PAN_LIMIT_CENTER: number = 3;\n    @Trace panLimit: number = 1;\n    @Trace scaledDensity: number = 0;\n    @Trace matrix: object = Matrix4.identity()\n      .rotate({ x: 0, y: 0, z: 1, angle: this.orientation })\n      .translate({ x: 0, y: 0 })\n      .scale({ x: this.scale, y: this.scale, centerX: this.touchCenterX, centerY: this.touchCenterY });\n\n    public setImage(src: string | PixelMap | Resource, previewSource?: string | Resource, state?: ImageViewState): Model {\n      this.src = src;\n      if (!!previewSource) {\n        this.previewSource = previewSource;\n      }\n      if (state != null) {\n        this.scale = state.getScale();\n        this.orientation = state.getOrientation();\n        this.touchCenterX = state.getCenterX();\n        this.touchCenterY = state.getCenterY();\n      }\n      this.setScaleDensity();\n      return this;\n    }\n\n\n    private setScaleDensity() {\n      try {\n        this.scaledDensity = display.getDefaultDisplaySync().scaledDensity;\n      } catch (e) {\n        console.error(\"SubsamplingScaleImageView setScaledDensity error\")\n      }\n\n    }\n\n    public setScale(scale: number): Model {\n      this.scale = scale;\n      return this;\n    }\n\n    public getScale(): number {\n      return this.scale;\n    }\n\n    public setMaxScale(maxScale: number): Model {\n      this.maxScale = maxScale;\n      return this;\n    }\n\n    public setMinScale(minScale: number): Model {\n      this.minScale = minScale;\n      return this;\n    }\n\n    public setQuickScaleEnabled(quickScaleEnabled: boolean): Model {\n      this.quickScaleEnabled = quickScaleEnabled;\n      return this;\n    }\n\n    public isPanEnabled(): boolean {\n      return this.panEnabled;\n    }\n\n    public setPanEnabled(panEnabled: boolean): Model {\n      this.panEnabled = panEnabled;\n      return this;\n    }\n\n    public isZoomEnabled(): boolean {\n      return this.zoomEnabled;\n    }\n\n    public setDoubleZoom(doubleEnabled: boolean): Model {\n      this.doubleEnabled = doubleEnabled;\n      return this;\n    }\n\n    public setZoomEnabled(zoomEnabled: boolean): Model {\n      this.zoomEnabled = zoomEnabled;\n      return this;\n    }\n\n    public setTileBackgroundColor(tileBgColor: number): Model {\n      this.tileBgColor = tileBgColor;\n      return this;\n    }\n\n    public getSWidth(): number {\n      return this.sWidth;\n    }\n\n    public getSHeight(): number {\n      return this.sHeight;\n    }\n\n    public canScroll(): boolean {\n      if (this.zoomEnabled && this.getScale() > 1) {\n        return true;\n      } else {\n        return false;\n      }\n    }\n\n    public setOrientation(degrees: number): Model {\n      this.orientation = degrees;\n      this.updateMatrix();\n      return this;\n    }\n\n    public setDoubleTapListener(listener: OnDoubleTapListener): Model {\n      this.doubleTapListener = listener;\n      return this;\n    }\n\n    public setSingleTapListener(listener: OnSingleTapListener): Model {\n      this.singleTapListener = listener;\n      return this;\n    }\n\n    public setLongPressListener(listener: OnLongPressListener): Model {\n      this.longPressListener = listener;\n      return this;\n    }\n\n    \n    public setOnStateChangedListener(onStateChangedListener: OnStateChangedListener): Model {\n      this.onStateChangedListener = onStateChangedListener;\n      return this;\n    }\n\n    public setOnImageEventListener(onImageEventListener: OnImageEventListener): Model {\n      this.onImageEventListener = onImageEventListener;\n      return this;\n    }\n\n    public getMaxScale(): number {\n      return this.maxScale;\n    }\n\n    public getMinScale(): number {\n      return this.minScale;\n    }\n\n    public setDoubleScale(localX: number, localY: number): void {\n      let distanceCenterX = this.componentWidth / 2 - vp2px(localX) + this.offsetX;\n      let distanceCenterY = this.componentHeight / 2 - vp2px(localY) + this.offsetY\n      this.scale = this.getMaxScale();\n      this.touchCenterX = 10\n      this.touchCenterY = 10\n      this.startOffsetX = distanceCenterX\n      this.startOffsetY = distanceCenterY\n      this.setOffset(distanceCenterX, distanceCenterY)\n    }\n\n    public setOffset(offsetX: number, offsetY: number): void {\n      this.offsetX = offsetX;\n      this.offsetY = offsetY;\n\n      this.updateMatrix();\n    }\n\n    public updateMatrix(): void {\n      this.matrix = Matrix4.identity()\n        .rotate({ x: 0, y: 0, z: 1, angle: this.orientation })\n        .translate({ x: this.offsetX, y: this.offsetY })\n        .scale({ x: this.scale, y: this.scale, centerX: this.touchCenterX, centerY: this.touchCenterY })\n    }\n\n    public resetScaleAndCenter(): void {\n      this.scale = 1;\n      this.baseScale = 1;\n      this.offsetX = 0;\n      this.offsetY = 0;\n      this.startOffsetX = 0;\n      this.startOffsetY = 0;\n      this.touchCenterX = 0;\n      this.touchCenterY = 0;\n      this.orientation = 0;\n      this.updateMatrix();\n    }\n\n    public viewToSourceX(vx: number): number {\n      return (vx - this.offsetX) * this.scaledDensity / this.scale;\n    }\n\n    \n    public viewToSourceY(vy: number): number {\n      return (vy - this.offsetY) * this.scaledDensity / this.scale;\n    }\n\n    public setDoubleTapZoomScale(doubleTapZoomScale: number): Model {\n      this.doubleTapZoomScale = doubleTapZoomScale;\n      return this;\n    }\n\n    \n    public isReady(): boolean {\n      return this.readySent;\n    }\n\n    public setPreferredBitmapConfig(preferredBitmapConfig: Config): Model {\n      this.preferredBitmapConfig = preferredBitmapConfig;\n      return this;\n    }\n\n    public getPreferredBitmapConfig(): Config {\n      return this.preferredBitmapConfig;\n    }\n\n    \n    public setDoubleTapZoomDuration(durationMs: number): Model {\n      this.doubleTapZoomDuration = Math.max(0, durationMs);\n      return this;\n    }\n\n    \n    public isImageLoaded(): boolean {\n      return this.imageLoadedSent;\n    }\n\n    \n    public setMinimumDpi(dpi: number): Model {\n      let screen = display.getDefaultDisplaySync()\n      let averageDpi: number = (screen.xDPI + screen.yDPI) / 2\n      this.setMaxScale(averageDpi / dpi);\n      return this;\n    }\n\n    \n    public setMaximumDpi(dpi: number): Model {\n      let screen = display.getDefaultDisplaySync()\n      let averageDpi: number = (screen.xDPI + screen.yDPI) / 2\n      this.setMinScale(averageDpi / dpi);\n      return this;\n    }\n\n    public getCenterX(): number {\n      return this.viewToSourceX(this.componentWidth / 2);\n    }\n\n    public getCenterY(): number {\n      return this.viewToSourceY(this.componentHeight / 2);\n    }\n\n    public getOrientation(): number {\n      return this.orientation;\n    }\n\n    public getState(): ImageViewState | null {\n      if (this.sWidth > 0 && this.sHeight > 0) {\n        \n        return new ImageViewState(this.getScale(), this.getCenterX(), this.getCenterY(), this.getOrientation());\n      }\n      return new ImageViewState(0, 0, 0, 0);\n    }\n\n    \n    public setPanLimit(panLimit: number): Model {\n      if (panLimit != this.PAN_LIMIT_INSIDE || panLimit != this.PAN_LIMIT_OUTSIDE || panLimit != this.PAN_LIMIT_CENTER) {\n        console.error(\"Invalid pan limit: \" + panLimit)\n      }\n      this.panLimit = panLimit;\n      return this;\n    }\n  }\n}\nexport { SubsamplingScaleImageView };"}
{"content": "/**\n * Copyright (C) 2022 Huawei Device Co., Ltd.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class SVGCircle {\n  private _cx: number = 0;\n  private _cy: number = 0;\n  private _r: number = 0;\n  private _circleResultObj: Record<string,number|string> = {  };\n\n  /**\n   * 获取圆心x坐标\n   */\n  public getCX(): number{\n    return this._cx;\n  }\n\n  /**\n   * 设置圆心x坐标\n   * @param newCX 圆心x坐标值\n   */\n  public setCX(newCX: number): void{\n    this._cx = newCX;\n    this._circleResultObj['cx'] = newCX;\n  }\n\n  /**\n   * 获取圆心y坐标\n   */\n  public getCY(): number{\n    return this._cy;\n  }\n\n  /**\n   * 设置圆心y坐标\n   * @param newCY 圆心y坐标值\n   */\n  public setCY(newCY: number): void{\n    this._cy = newCY;\n    this._circleResultObj['cy'] = newCY;\n  }\n\n  /**\n   * 获取圆半径\n   */\n  public getR(): number{\n    return this._r;\n  }\n\n  /**\n   * 设置圆半径\n   * @param newR 矩形宽度\n   */\n  public setR(newR: number): void{\n    this._r = newR;\n    this._circleResultObj['r'] = newR;\n  }\n\n  public addAttribute(key: string, value: string): void{\n    this._circleResultObj[key] = value;\n  }\n\n  public toObj(): object{\n    return this._circleResultObj;\n  }\n}\n", "filepath": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs_nocomments/XmlGraphicsBatik/library/src/main/ets/batik/svggen/SVGCircle.ets", "repo": "XmlGraphicsBatik", "isArkUI": false, "functions": [{"name": "setCX", "comment": "* 设置圆心x坐标\n   * @param newCX 圆心x坐标值", "body": "public setCX(newCX: number): void{\n    this._cx = newCX;\n    this._circleResultObj['cx'] = newCX;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 2, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 4, "Relevance": 3.5}, "comment_zh": "设置圆心x坐标"}, {"name": "setCY", "comment": "* 设置圆心y坐标\n   * @param newCY 圆心y坐标值", "body": "public setCY(newCY: number): void{\n    this._cy = newCY;\n    this._circleResultObj['cy'] = newCY;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 2}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 4, "Relevance": 3.5}, "comment_zh": "设置圆心y坐标"}], "token_count": 489, "content_with_no_comment": "\n\nexport class SVGCircle {\n  private _cx: number = 0;\n  private _cy: number = 0;\n  private _r: number = 0;\n  private _circleResultObj: Record<string,number|string> = {  };\n\n  \n  public getCX(): number{\n    return this._cx;\n  }\n\n  \n  public setCX(newCX: number): void{\n    this._cx = newCX;\n    this._circleResultObj['cx'] = newCX;\n  }\n\n  \n  public getCY(): number{\n    return this._cy;\n  }\n\n  \n  public setCY(newCY: number): void{\n    this._cy = newCY;\n    this._circleResultObj['cy'] = newCY;\n  }\n\n  \n  public getR(): number{\n    return this._r;\n  }\n\n  \n  public setR(newR: number): void{\n    this._r = newR;\n    this._circleResultObj['r'] = newR;\n  }\n\n  public addAttribute(key: string, value: string): void{\n    this._circleResultObj[key] = value;\n  }\n\n  public toObj(): object{\n    return this._circleResultObj;\n  }\n}\n"}
{"content": "/**\n * Copyright (C) 2022 Huawei Device Co., Ltd.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class SVGRect {\n  private _x: number = 0;\n  private _y: number = 0;\n  private _width: number = 0;\n  private _height: number = 0;\n  private _rx: number = 0\n  private _ry: number = 0\n  private _rectResultObj: Record<string,string|number> = {  };\n\n  /**\n   * 获取x坐标\n   */\n  public getX(): number{\n    return this._x;\n  }\n\n  /**\n   * 设置x坐标\n   * @param newX x坐标值\n   */\n  public setX(newX: number): void{\n    this._x = newX;\n    this._rectResultObj['x'] = newX;\n  }\n\n  /**\n   * 获取y坐标\n   */\n  public getY(): number{\n    return this._y;\n  }\n\n  /**\n   * 设置y坐标\n   * @param newY y坐标值\n   */\n  public setY(newY: number): void{\n    this._y = newY;\n    this._rectResultObj['y'] = newY;\n  }\n\n  /**\n   * 获取矩形宽度\n   */\n  public getWidth(): number{\n    return this._width;\n  }\n\n  /**\n   * 设置宽度\n   * @param newWidth 矩形宽度\n   */\n  public setWidth(newWidth: number): void{\n    this._width = newWidth;\n    this._rectResultObj['width'] = newWidth;\n  }\n\n  /**\n   * 获取矩形高度\n   */\n  public getHeight(): number{\n    return this._height;\n  }\n\n  public setHeight(newHeight: number): void{\n    this._height = newHeight;\n    this._rectResultObj['height'] = newHeight;\n  }\n\n  public getRX(): number{\n    return this._rx;\n  }\n\n  public setRX(newRX: number): void{\n    this._rx = newRX;\n    this._rectResultObj['rx'] = newRX;\n  }\n\n  public getRY(): number{\n    return this._ry;\n  }\n\n  public setRY(newRY: number): void{\n    this._ry = newRY;\n    this._rectResultObj['ry'] = newRY;\n  }\n\n  public addAttribute(key: string, value: string): void{\n    this._rectResultObj[key] = value;\n  }\n\n  public toObj(): object{\n    return this._rectResultObj;\n  }\n}\n", "filepath": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs_nocomments/XmlGraphicsBatik/library/src/main/ets/batik/svggen/SVGRect.ets", "repo": "XmlGraphicsBatik", "isArkUI": false, "functions": [{"name": "getX", "comment": "* 获取x坐标", "body": "public getX(): number{\n    return this._x;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 2, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 0, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 3.5, "Fluency": 3, "Relevance": 4}, "comment_zh": "获取x坐标"}, {"name": "getY", "comment": "* 获取y坐标", "body": "public getY(): number{\n    return this._y;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 0, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 0, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 2, "Relevance": 4}, "comment_zh": "获取y坐标"}], "token_count": 651, "content_with_no_comment": "\n\nexport class SVGRect {\n  private _x: number = 0;\n  private _y: number = 0;\n  private _width: number = 0;\n  private _height: number = 0;\n  private _rx: number = 0\n  private _ry: number = 0\n  private _rectResultObj: Record<string,string|number> = {  };\n\n  \n  public getX(): number{\n    return this._x;\n  }\n\n  \n  public setX(newX: number): void{\n    this._x = newX;\n    this._rectResultObj['x'] = newX;\n  }\n\n  \n  public getY(): number{\n    return this._y;\n  }\n\n  \n  public setY(newY: number): void{\n    this._y = newY;\n    this._rectResultObj['y'] = newY;\n  }\n\n  \n  public getWidth(): number{\n    return this._width;\n  }\n\n  \n  public setWidth(newWidth: number): void{\n    this._width = newWidth;\n    this._rectResultObj['width'] = newWidth;\n  }\n\n  \n  public getHeight(): number{\n    return this._height;\n  }\n\n  public setHeight(newHeight: number): void{\n    this._height = newHeight;\n    this._rectResultObj['height'] = newHeight;\n  }\n\n  public getRX(): number{\n    return this._rx;\n  }\n\n  public setRX(newRX: number): void{\n    this._rx = newRX;\n    this._rectResultObj['rx'] = newRX;\n  }\n\n  public getRY(): number{\n    return this._ry;\n  }\n\n  public setRY(newRY: number): void{\n    this._ry = newRY;\n    this._rectResultObj['ry'] = newRY;\n  }\n\n  public addAttribute(key: string, value: string): void{\n    this._rectResultObj[key] = value;\n  }\n\n  public toObj(): object{\n    return this._rectResultObj;\n  }\n}\n"}
{"content": "/**\n * Copyright (C) 2022 Huawei Device Co., Ltd.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\nimport RegexConstants from './constants/RegexConstants'\n\nexport class StringReader {\n  // 传入的xml字符串转成的字符数组\n  private _chars: string[] = new Array();\n\n  // 字符数组的长度\n  private _charCount: number = 0;\n\n  // 字符数组转成bytes数组\n  private _charsToBytes: number[] = new Array();\n\n  // 是否为多字节模式\n  private _multiByteMode: boolean = false;\n\n  // 读取到的字符索引位置\n  public charIndex: number = 0;\n\n  // XML文件字符串\n  public xmlDetailInfo: string = '';\n\n  // 空字符\n  public emptyString: string = '';\n\n  constructor(xmlInfo:string) {\n    let xmlInfoArray: string[] = xmlInfo.split('')\n    this._chars =xmlInfoArray\n    this._charCount = this._chars.length;\n    this._charsToBytes = new Array(this._charCount);\n    this._multiByteMode = false;\n    this.charIndex = 0;\n    this.xmlDetailInfo = xmlInfo;\n\n   let _chars= this._chars;\n   let _charCount=this._charCount;\n   let _charsToBytes=this._charsToBytes\n\n\n    if (_charCount === xmlInfo.length) {\n\n      // 输入的字符串中没有多字节字符，所以char索引与byte索引相同\n      for (let i = 0; i < _charCount; ++i) {\n        _charsToBytes[i] = i;\n      }\n    } else {\n\n      // 当字符串中包含多字节字符时，字节索引与字符索引不相等\n      for (let byteIndex = 0, charIndex = 0; charIndex < _charCount; ++charIndex) {\n        _charsToBytes[charIndex] = byteIndex;\n        byteIndex += _chars[charIndex].length;\n      }\n\n      this._multiByteMode = true;\n    }\n  }\n\n  /*\n   * 返回字符数，如果字符串中包含多字节字符，字符长度可能与字节长度不同\n   * @param string\n   */\n  private _getCharLength(characters: string): number {\n    let  length:number=characters.length\n\n    if (length < 2 || !this._multiByteMode) {\n      return length;\n    }\n\n    return characters.replace(RegexConstants.REGEX_MULTIBYTE, '_').length;\n  }\n\n  // -- Public Methods ---------------------------------------------------------\n  /*\n   * 判断当前读取的字符位置是否为文档最后\n   */\n  public isEnd(): boolean{\n    return this.charIndex >= this._charCount;\n  }\n\n  /*\n   * 更新字符索引\n   * @param count = 1 移动的字符数\n   */\n  public moveNext(count: number = 1): void {\n    this.charIndex = Math.min(this._charCount, this.charIndex + count);\n  }\n\n  /*\n   * 继续读取给定字符数，如果到达字符串末尾，则停止。读不到时返回空字符串\n   * @param count 读取的字符数\n   * @return 读取到的字符串\n   */\n  public readByCount(count: number= 1): string {\n    let characters = this.peek(count);\n    this.moveNext(count);\n    return characters;\n  }\n\n  /*\n   * 读取符合正则表达式的字符\n   * @param {RegExp}regex 返回符合正则表达式的字符\n   * @return 读取到的符合正则表达式的字符\n   */\n  public readMatchRegex(regex:RegExp): string {\n    if (!regex.sticky) {\n      throw new Error('`regex` must have a sticky flag (\"y\")');\n    }\n\n    regex.lastIndex = this._charsToBytes[this.charIndex];\n\n    let result = regex.exec(this.xmlDetailInfo);\n\n    if (result === null) {\n      return this.emptyString;\n    }\n\n    let match = result[0];\n    this.moveNext(this._getCharLength(match));\n    return match;\n  }\n\n  /**\n   * 在给定函数返回真值时读取字符，在返回假值或输入结束时停止。\n   * @param {(char: string) => boolean} fn\n   * @return 返回 读取到的符合条件的字符串\n   */\n  public readByFunction(fn: (char: string) => boolean): string {\n    let startIndex = this.charIndex;\n\n    while (!this.isEnd() && fn(this.peek())) {\n      this.moveNext();\n    }\n\n    return this.charIndex > startIndex\n      ? this.xmlDetailInfo.slice(this._charsToBytes[startIndex], this._charsToBytes[this.charIndex])\n      : this.emptyString;\n  }\n\n  /**\n   * 如果给定的字符串在当前的字符索引中，读取字符串并更新索引位置\n   * @param pendingReadString 要读取的字符串\n   * @return 返回要被读取的字符串，未读取到返回空字符串\n   */\n  public ReadByString(pendingReadString: string): string {\n    if (this.readStringFast(pendingReadString)) {\n      return pendingReadString;\n    }\n\n    if (!this._multiByteMode) {\n      return this.emptyString;\n    }\n\n    let  length :number= pendingReadString.length;\n    let charLengthToMatch = this._getCharLength(pendingReadString);\n\n    if (charLengthToMatch !== length\n    && pendingReadString === this.peek(charLengthToMatch)) {\n\n      this.moveNext(charLengthToMatch);\n      return pendingReadString;\n    }\n\n    return this.emptyString;\n  }\n\n  /**\n   * 读取给定字符\n   * @param stringToConsume 要读取的字符\n   * @return 要被读取的字符串\n   */\n  public readStringFast(pendingReadString: string): string {\n    if (this.peek() === pendingReadString[0]) {\n      let  length:number = pendingReadString.length;\n\n      if (length === 1) {\n        this.moveNext();\n        return pendingReadString;\n      }\n\n      if (this.peek(length) === pendingReadString) {\n        this.moveNext(length);\n        return pendingReadString;\n      }\n    }\n\n    return this.emptyString;\n  }\n\n  /*\n   * 读取到符合给定的全局正则表达式的字符串位置的字符串，更新lastIndex\n   * @param regex 带有全局标识的正则表达式\n   * @return 返回到符合正则表达式部分的字符串\n   */\n  public readUntilMatch(regex: RegExp): string{\n    if (!regex.global) {\n      throw new Error('`regex` must have a global flag (\"g\")');\n    }\n\n    let byteIndex = this._charsToBytes[this.charIndex];\n    regex.lastIndex = byteIndex;\n\n    let match = regex.exec(this.xmlDetailInfo);\n\n    if (match === null || match.index === byteIndex) {\n      return this.emptyString;\n    }\n\n    let result = this.xmlDetailInfo.slice(byteIndex, match.index);\n    this.moveNext(this._getCharLength(result));\n    return result;\n  }\n\n  /*\n   * 读取从当前位置到符合搜索条件的字符串的全部字符串，如果没找到符合条件的字符串，则返回空字符串\n   * @param searchString  将要查找的字符串\n   * @return 返回到给定字符串部分的字符串\n   */\n  public readUntilString(searchString: string): string {\n\n    let charIndex: number=this.charIndex\n    let _charsToBytes: number[]=this._charsToBytes\n    let xmlDetailInfo: string=this.xmlDetailInfo\n\n    let byteIndex = _charsToBytes[charIndex];\n    let matchByteIndex = xmlDetailInfo.indexOf(searchString, byteIndex);\n\n    if (matchByteIndex <= 0) {\n      return this.emptyString;\n    }\n\n    let result = xmlDetailInfo.slice(byteIndex, matchByteIndex);\n    this.moveNext(this._getCharLength(result));\n    return result;\n  }\n\n  /*\n   * 获取从当前索引位置开始的给定字符数的字符\n   * @param count：要获取的字符数\n   * @return 返回给定数量的字符串\n   */\n  public peek(count: number = 1): string {\n    if (this.charIndex >= this._charCount) {\n      return this.emptyString;\n    }\n\n    if (count === 1) {\n      return this._chars[this.charIndex];\n    }\n\n\n    let _charsToBytes: number[]=this._charsToBytes\n    let charIndex: number=this.charIndex\n    return this.xmlDetailInfo.slice(_charsToBytes[charIndex], _charsToBytes[charIndex + count]);\n  }\n\n  /*\n   * 重置字符索引为给定的位置，如果未给出索引位置，则重置为输入字符串的开头。\n   * @param index 索引位置，如果索引为负数，向后移动指定字符数，直至字符串开头\n   */\n  public reset(index: number = 0): void {\n    this.charIndex = index >= 0\n      ? Math.min(this._charCount, index)\n      : Math.max(0, this.charIndex + index);\n  }\n}", "filepath": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs_nocomments/XmlGraphicsBatik/library/src/main/ets/batik/StringReader.ets", "repo": "XmlGraphicsBatik", "isArkUI": false, "functions": [{"name": "isEnd", "comment": "* 判断当前读取的字符位置是否为文档最后", "body": "public isEnd(): boolean{\n    return this.charIndex >= this._charCount;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 2, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 3.5, "Fluency": 4, "Relevance": 4}, "comment_zh": "判断当前读取的字符位置是否为文档最后"}, {"name": "ReadByString", "comment": "* 如果给定的字符串在当前的字符索引中，读取字符串并更新索引位置\n   * @param pendingReadString 要读取的字符串\n   * @return 返回要被读取的字符串，未读取到返回空字符串", "body": "public ReadByString(pendingReadString: string): string {\n    if (this.readStringFast(pendingReadString)) {\n      return pendingReadString;\n    }\n\n    if (!this._multiByteMode) {\n      return this.emptyString;\n    }\n\n    let  length :number= pendingReadString.length;\n    let charLengthToMatch = this._getCharLength(pendingReadString);\n\n    if (charLengthToMatch !== length\n    && pendingReadString === this.peek(charLengthToMatch)) {\n\n      this.moveNext(charLengthToMatch);\n      return pendingReadString;\n    }\n\n    return this.emptyString;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 3, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 3}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 4, "Relevance": 3.5}, "comment_zh": "如果给定的字符串在当前的字符索引中，读取字符串并更新索引位置。返回要被读取的字符串，未读取到返回空字符串。"}, {"name": "readUntilMatch", "comment": "* 读取到符合给定的全局正则表达式的字符串位置的字符串，更新lastIndex\n   * @param regex 带有全局标识的正则表达式\n   * @return 返回到符合正则表达式部分的字符串", "body": "public readUntilMatch(regex: RegExp): string{\n    if (!regex.global) {\n      throw new Error('`regex` must have a global flag (\"g\")');\n    }\n\n    let byteIndex = this._charsToBytes[this.charIndex];\n    regex.lastIndex = byteIndex;\n\n    let match = regex.exec(this.xmlDetailInfo);\n\n    if (match === null || match.index === byteIndex) {\n      return this.emptyString;\n    }\n\n    let result = this.xmlDetailInfo.slice(byteIndex, match.index);\n    this.moveNext(this._getCharLength(result));\n    return result;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 3, "Code Reviewer_Relevance": 3, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 3, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 2, "Code Editor_Relevance": 3, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 3, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 2.75, "Relevance": 3.5}, "comment_zh": "读取到符合给定的全局正则表达式的字符串位置的字符串，更新lastIndex。返回到符合正则表达式部分的字符串。"}, {"name": "readUntilString", "comment": "* 读取从当前位置到符合搜索条件的字符串的全部字符串，如果没找到符合条件的字符串，则返回空字符串\n   * @param searchString  将要查找的字符串\n   * @return 返回到给定字符串部分的字符串", "body": "public readUntilString(searchString: string): string {\n\n    let charIndex: number=this.charIndex\n    let _charsToBytes: number[]=this._charsToBytes\n    let xmlDetailInfo: string=this.xmlDetailInfo\n\n    let byteIndex = _charsToBytes[charIndex];\n    let matchByteIndex = xmlDetailInfo.indexOf(searchString, byteIndex);\n\n    if (matchByteIndex <= 0) {\n      return this.emptyString;\n    }\n\n    let result = xmlDetailInfo.slice(byteIndex, matchByteIndex);\n    this.moveNext(this._getCharLength(result));\n    return result;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 3, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 3, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 3, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 3.25, "Relevance": 4}, "comment_zh": "读取从当前位置到符合搜索条件的字符串的全部内容，若未找到匹配字符串则返回空字符串"}, {"name": "reset", "comment": "* 重置字符索引为给定的位置，如果未给出索引位置，则重置为输入字符串的开头。\n   * @param index 索引位置，如果索引为负数，向后移动指定字符数，直至字符串开头", "body": "public reset(index: number = 0): void {\n    this.charIndex = index >= 0\n      ? Math.min(this._charCount, index)\n      : Math.max(0, this.charIndex + index);\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 3, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 3, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 3, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 2, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 3.5, "Fluency": 4, "Relevance": 3.25}, "comment_zh": "将字符索引重置到指定位置，如未提供索引位置则重置至输入字符串起始处。  \n    @param index 索引位置，若为负数则向后移动相应字符数（直至字符串开头）"}], "token_count": 2102, "content_with_no_comment": "\n\n\nimport RegexConstants from './constants/RegexConstants'\n\nexport class StringReader {\n  \n  private _chars: string[] = new Array();\n\n  \n  private _charCount: number = 0;\n\n  \n  private _charsToBytes: number[] = new Array();\n\n  \n  private _multiByteMode: boolean = false;\n\n  \n  public charIndex: number = 0;\n\n  \n  public xmlDetailInfo: string = '';\n\n  \n  public emptyString: string = '';\n\n  constructor(xmlInfo:string) {\n    let xmlInfoArray: string[] = xmlInfo.split('')\n    this._chars =xmlInfoArray\n    this._charCount = this._chars.length;\n    this._charsToBytes = new Array(this._charCount);\n    this._multiByteMode = false;\n    this.charIndex = 0;\n    this.xmlDetailInfo = xmlInfo;\n\n   let _chars= this._chars;\n   let _charCount=this._charCount;\n   let _charsToBytes=this._charsToBytes\n\n\n    if (_charCount === xmlInfo.length) {\n\n      \n      for (let i = 0; i < _charCount; ++i) {\n        _charsToBytes[i] = i;\n      }\n    } else {\n\n      \n      for (let byteIndex = 0, charIndex = 0; charIndex < _charCount; ++charIndex) {\n        _charsToBytes[charIndex] = byteIndex;\n        byteIndex += _chars[charIndex].length;\n      }\n\n      this._multiByteMode = true;\n    }\n  }\n\n  \n  private _getCharLength(characters: string): number {\n    let  length:number=characters.length\n\n    if (length < 2 || !this._multiByteMode) {\n      return length;\n    }\n\n    return characters.replace(RegexConstants.REGEX_MULTIBYTE, '_').length;\n  }\n\n  \n  \n  public isEnd(): boolean{\n    return this.charIndex >= this._charCount;\n  }\n\n  \n  public moveNext(count: number = 1): void {\n    this.charIndex = Math.min(this._charCount, this.charIndex + count);\n  }\n\n  \n  public readByCount(count: number= 1): string {\n    let characters = this.peek(count);\n    this.moveNext(count);\n    return characters;\n  }\n\n  \n  public readMatchRegex(regex:RegExp): string {\n    if (!regex.sticky) {\n      throw new Error('`regex` must have a sticky flag (\"y\")');\n    }\n\n    regex.lastIndex = this._charsToBytes[this.charIndex];\n\n    let result = regex.exec(this.xmlDetailInfo);\n\n    if (result === null) {\n      return this.emptyString;\n    }\n\n    let match = result[0];\n    this.moveNext(this._getCharLength(match));\n    return match;\n  }\n\n  \n  public readByFunction(fn: (char: string) => boolean): string {\n    let startIndex = this.charIndex;\n\n    while (!this.isEnd() && fn(this.peek())) {\n      this.moveNext();\n    }\n\n    return this.charIndex > startIndex\n      ? this.xmlDetailInfo.slice(this._charsToBytes[startIndex], this._charsToBytes[this.charIndex])\n      : this.emptyString;\n  }\n\n  \n  public ReadByString(pendingReadString: string): string {\n    if (this.readStringFast(pendingReadString)) {\n      return pendingReadString;\n    }\n\n    if (!this._multiByteMode) {\n      return this.emptyString;\n    }\n\n    let  length :number= pendingReadString.length;\n    let charLengthToMatch = this._getCharLength(pendingReadString);\n\n    if (charLengthToMatch !== length\n    && pendingReadString === this.peek(charLengthToMatch)) {\n\n      this.moveNext(charLengthToMatch);\n      return pendingReadString;\n    }\n\n    return this.emptyString;\n  }\n\n  \n  public readStringFast(pendingReadString: string): string {\n    if (this.peek() === pendingReadString[0]) {\n      let  length:number = pendingReadString.length;\n\n      if (length === 1) {\n        this.moveNext();\n        return pendingReadString;\n      }\n\n      if (this.peek(length) === pendingReadString) {\n        this.moveNext(length);\n        return pendingReadString;\n      }\n    }\n\n    return this.emptyString;\n  }\n\n  \n  public readUntilMatch(regex: RegExp): string{\n    if (!regex.global) {\n      throw new Error('`regex` must have a global flag (\"g\")');\n    }\n\n    let byteIndex = this._charsToBytes[this.charIndex];\n    regex.lastIndex = byteIndex;\n\n    let match = regex.exec(this.xmlDetailInfo);\n\n    if (match === null || match.index === byteIndex) {\n      return this.emptyString;\n    }\n\n    let result = this.xmlDetailInfo.slice(byteIndex, match.index);\n    this.moveNext(this._getCharLength(result));\n    return result;\n  }\n\n  \n  public readUntilString(searchString: string): string {\n\n    let charIndex: number=this.charIndex\n    let _charsToBytes: number[]=this._charsToBytes\n    let xmlDetailInfo: string=this.xmlDetailInfo\n\n    let byteIndex = _charsToBytes[charIndex];\n    let matchByteIndex = xmlDetailInfo.indexOf(searchString, byteIndex);\n\n    if (matchByteIndex <= 0) {\n      return this.emptyString;\n    }\n\n    let result = xmlDetailInfo.slice(byteIndex, matchByteIndex);\n    this.moveNext(this._getCharLength(result));\n    return result;\n  }\n\n  \n  public peek(count: number = 1): string {\n    if (this.charIndex >= this._charCount) {\n      return this.emptyString;\n    }\n\n    if (count === 1) {\n      return this._chars[this.charIndex];\n    }\n\n\n    let _charsToBytes: number[]=this._charsToBytes\n    let charIndex: number=this.charIndex\n    return this.xmlDetailInfo.slice(_charsToBytes[charIndex], _charsToBytes[charIndex + count]);\n  }\n\n  \n  public reset(index: number = 0): void {\n    this.charIndex = index >= 0\n      ? Math.min(this._charCount, index)\n      : Math.max(0, this.charIndex + index);\n  }\n}"}
{"content": "/**\n * Copyright (C) 2022 Huawei Device Co., Ltd.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class SVGEllipse {\n  private _cx: number = 0;\n  private _cy: number = 0;\n  private _rx: number = 0;\n  private _ry: number = 0;\n  private _ellipseResultObj:Record<string,string|number> = { };\n\n  /**\n   * 获取椭圆中心点x坐标\n   */\n  public getCX(): number{\n    return this._cx;\n  }\n\n  /**\n   * 设置椭圆中心点x坐标\n   * @param newCX x坐标值\n   */\n  public setCX(newCX: number): void{\n    this._cx = newCX;\n    this._ellipseResultObj['cx'] = newCX;\n  }\n\n  /**\n   * 获取椭圆中心点y坐标\n   */\n  public getCY(): number{\n    return this._cy;\n  }\n\n  /**\n   * 设置椭圆中心点y坐标\n   * @param newCY y坐标值\n   */\n  public setCY(newCY: number): void{\n    this._cy = newCY;\n    this._ellipseResultObj['cy'] = newCY;\n  }\n\n  /**\n   * 获取椭圆X半径\n   */\n  public getRX(): number{\n    return this._rx;\n  }\n\n  /**\n   * 设置椭圆X半径\n   * @param newRX 椭圆X半径\n   */\n  public setRX(newRX: number): void{\n    this._rx = newRX;\n    this._ellipseResultObj['rx'] = newRX;\n  }\n\n  /**\n   * 获取椭圆y半径\n   */\n  public getRY(): number{\n    return this._ry;\n  }\n\n  /**\n   * 获取椭圆y半径\n   * @param newRY 椭圆y半径\n   */\n  public setRY(newRY: number): void{\n    this._ry = newRY;\n    this._ellipseResultObj['ry'] = newRY;\n  }\n\n  /**\n   * 添加共通属性\n   * @param key 主键\n   * @param value 值\n   */\n  public addAttribute(key: string, value: string): void {\n    this._ellipseResultObj[key] = value;\n  }\n\n  public toObj(): object{\n    return this._ellipseResultObj;\n  }\n}\n", "filepath": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs_nocomments/XmlGraphicsBatik/library/src/main/ets/batik/svggen/SVGEllipse.ets", "repo": "XmlGraphicsBatik", "isArkUI": false, "functions": [{"name": "setCX", "comment": "* 设置椭圆中心点x坐标\n   * @param newCX x坐标值", "body": "public setCX(newCX: number): void{\n    this._cx = newCX;\n    this._ellipseResultObj['cx'] = newCX;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 3, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 3, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 2, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 4, "Relevance": 3}, "comment_zh": "设置椭圆中心点x坐标 "}, {"name": "setCY", "comment": "* 设置椭圆中心点y坐标\n   * @param newCY y坐标值", "body": "public setCY(newCY: number): void{\n    this._cy = newCY;\n    this._ellipseResultObj['cy'] = newCY;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 3, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 4, "Relevance": 3.75}, "comment_zh": "设置椭圆中心点y坐标"}, {"name": "setRX", "comment": "* 设置椭圆X半径\n   * @param newRX 椭圆X半径", "body": "public setRX(newRX: number): void{\n    this._rx = newRX;\n    this._ellipseResultObj['rx'] = newRX;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 2, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 3, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 4, "Relevance": 3.25}, "comment_zh": "设置椭圆X半径"}], "token_count": 625, "content_with_no_comment": "\n\nexport class SVGEllipse {\n  private _cx: number = 0;\n  private _cy: number = 0;\n  private _rx: number = 0;\n  private _ry: number = 0;\n  private _ellipseResultObj:Record<string,string|number> = { };\n\n  \n  public getCX(): number{\n    return this._cx;\n  }\n\n  \n  public setCX(newCX: number): void{\n    this._cx = newCX;\n    this._ellipseResultObj['cx'] = newCX;\n  }\n\n  \n  public getCY(): number{\n    return this._cy;\n  }\n\n  \n  public setCY(newCY: number): void{\n    this._cy = newCY;\n    this._ellipseResultObj['cy'] = newCY;\n  }\n\n  \n  public getRX(): number{\n    return this._rx;\n  }\n\n  \n  public setRX(newRX: number): void{\n    this._rx = newRX;\n    this._ellipseResultObj['rx'] = newRX;\n  }\n\n  \n  public getRY(): number{\n    return this._ry;\n  }\n\n  \n  public setRY(newRY: number): void{\n    this._ry = newRY;\n    this._ellipseResultObj['ry'] = newRY;\n  }\n\n  \n  public addAttribute(key: string, value: string): void {\n    this._ellipseResultObj[key] = value;\n  }\n\n  public toObj(): object{\n    return this._ellipseResultObj;\n  }\n}\n"}
{"content": "/**\n * Copyright (C) 2022 Huawei Device Co., Ltd.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {ArrayTypeRange} from \"../util/ObjOrArrayUtil\";\nimport SVGAttrConstants from \"../constants/SVGAttrConstants\"\nimport {isArray} from '../tools/IsArrayFunction'\n/**\n * 处理SVG文件的\n * @param elementType\n */\nexport class SVGSpecifiedFormat {\n  // 承接SVG对象结果的对象\n  private _formatResultObj: Record<string,string|object> = {};\n  private _textObj: Record<string,string> = {};\n  private _elements: Array<Object>=[] ;\n\n\n  /**\n   * 设置节点类型\n   * @param elementType 节点类型\n   */\n  public setElementType(elementType: string): void{\n    let index = ArrayTypeRange.indexOf(elementType);\n    if (index === -1) {\n      return;\n    }\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_TYPE] = elementType;\n  }\n\n  /**\n   * 获取节点类型\n   */\n  public getElementType(): string{\n    return this._formatResultObj[SVGAttrConstants.ATTR_KEY_TYPE] as string;\n  }\n\n  /**\n   * 设置节点名称\n   * @param elementName 节点名称\n   */\n  public setElementName(elementName: string): void {\n    if (!elementName) {\n      return;\n    }\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_NAME] = elementName;\n  }\n\n  /**\n   * 获取节点名称\n   */\n  public getElementName(): string{\n    return this._formatResultObj[SVGAttrConstants.ATTR_KEY_NAME]as string;\n  }\n\n  /**\n   * 设置节点属性集合\n   * @param attributes 属性集合\n   */\n  public setAttributes(attributes: Object): void {\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_ATTRIBUTES] = attributes;\n  }\n\n  /**\n   * 获取属性集合\n   */\n  public getAttributes(): object{\n    return this._formatResultObj[SVGAttrConstants.ATTR_KEY_ATTRIBUTES]as  object;\n  }\n\n  /**\n   * 设置节点的子节点（覆盖原有子节点值）\n   * @param elements 子节点\n   */\n  public setElements(elementsInfo: Object): void{\n    if (isArray(elementsInfo)) {\n      this._elements = elementsInfo;\n    } else {\n      this._elements = new Array()\n      this._elements.push(elementsInfo);\n    }\n\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = this._elements;\n  }\n\n  /**\n   * 添加节点的子节点（不覆盖原有子节点值）\n   * @param elements 子节点\n   */\n  public addElements(elements: Object): void{\n    let objKeys: string[] = Object.keys(this._formatResultObj);\n    let indexOfKey = objKeys.indexOf(SVGAttrConstants.ATTR_KEY_ELEMENTS);\n    if (indexOfKey === -1) {\n      this._elements = new Array();\n      if (isArray(elements)) {\n        this._elements.concat(elements);\n      } else {\n        this._elements.push(elements);\n      }\n      this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = this._elements;\n    } else {\n      let elementsArray = this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS];\n      if (isArray(elementsArray)) {\n        if (isArray(elements)) {\n          elementsArray.concat(elements);\n        } else {\n          elementsArray.push(elements);\n        }\n        this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = elementsArray;\n      }\n    }\n  }\n\n  /**\n   * 获取当前节点的子节点\n   */\n  public getElements(): object{\n    return this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS]as object;\n  }\n\n  /**\n   * 设置当前节点的文本（覆盖原有文本）\n   * @param text 文本\n   */\n  public setElementsText(text: string): void{\n    this._elements = new Array();\n    this._textObj[SVGAttrConstants.ATTR_KEY_TYPE] = SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT;\n    this._textObj[SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT] = text;\n    this._elements.push(this._textObj);\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = this._elements;\n  }\n\n  /**\n   * 添加当前节点的文本（不覆盖原有文本）\n   * @param text 文本\n   */\n  public addElementsText(text: string): void{\n    let objKeys: string[] = Object.keys(this._formatResultObj);\n    let indexOfKey = objKeys.indexOf(SVGAttrConstants.ATTR_KEY_ELEMENTS);\n    this._textObj[SVGAttrConstants.ATTR_KEY_TYPE] = SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT;\n    this._textObj[SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT] = text;\n    if (indexOfKey === -1) {\n      this._elements = new Array();\n      this._elements.push(this._textObj);\n      this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = this._elements;\n    } else {\n      let elementsArray = this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS];\n      if (isArray(elementsArray)) {\n        elementsArray.push(this._textObj);\n        this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = elementsArray;\n      }\n    }\n  }\n\n  /**\n   * 获取标准格式节点内容\n   */\n  public toObj(): object{\n    return this._formatResultObj;\n  }\n}\n", "filepath": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs_nocomments/XmlGraphicsBatik/library/src/main/ets/batik/svggen/SVGSpecifiedFormat.ets", "repo": "XmlGraphicsBatik", "isArkUI": false, "functions": [{"name": "getElementType", "comment": "* 获取节点类型", "body": "public getElementType(): string{\n    return this._formatResultObj[SVGAttrConstants.ATTR_KEY_TYPE] as string;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 3, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 3, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 2, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 2, "Systems Analyst_Fluency": 0, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 3.75, "Consistency": 3, "Fluency": 3, "Relevance": 3.75}, "comment_zh": "获取节点类型"}, {"name": "setElementName", "comment": "* 设置节点名称\n   * @param elementName 节点名称", "body": "public setElementName(elementName: string): void {\n    if (!elementName) {\n      return;\n    }\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_NAME] = elementName;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 3, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 2, "Code Editor_Coherence": 4, "Code Editor_Consistency": 3, "Code Editor_Fluency": 4, "Code Editor_Relevance": 3, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 3.5, "Fluency": 4, "Relevance": 3.25}, "comment_zh": "设置节点名称"}, {"name": "getElementName", "comment": "* 获取节点名称", "body": "public getElementName(): string{\n    return this._formatResultObj[SVGAttrConstants.ATTR_KEY_NAME]as string;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 3, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 3, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 0, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 2.5, "Relevance": 4}, "comment_zh": "获取节点名称"}, {"name": "setAttributes", "comment": "* 设置节点属性集合\n   * @param attributes 属性集合", "body": "public setAttributes(attributes: Object): void {\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_ATTRIBUTES] = attributes;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 2, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 2, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 3, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 3}, "criterion_averages": {"Coherence": 3.5, "Consistency": 3.75, "Fluency": 4, "Relevance": 3.25}, "comment_zh": "设置节点属性集合"}, {"name": "getAttributes", "comment": "* 获取属性集合", "body": "public getAttributes(): object{\n    return this._formatResultObj[SVGAttrConstants.ATTR_KEY_ATTRIBUTES]as  object;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 0, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 0, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 0, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 1, "Relevance": 4}, "comment_zh": "获取属性集合"}, {"name": "addElements", "comment": "* 添加节点的子节点（不覆盖原有子节点值）\n   * @param elements 子节点", "body": "public addElements(elements: Object): void{\n    let objKeys: string[] = Object.keys(this._formatResultObj);\n    let indexOfKey = objKeys.indexOf(SVGAttrConstants.ATTR_KEY_ELEMENTS);\n    if (indexOfKey === -1) {\n      this._elements = new Array();\n      if (isArray(elements)) {\n        this._elements.concat(elements);\n      } else {\n        this._elements.push(elements);\n      }\n      this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = this._elements;\n    } else {\n      let elementsArray = this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS];\n      if (isArray(elementsArray)) {\n        if (isArray(elements)) {\n          elementsArray.concat(elements);\n        } else {\n          elementsArray.push(elements);\n        }\n        this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = elementsArray;\n      }\n    }\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 2, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 2, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 3.5, "Fluency": 4, "Relevance": 3.5}, "comment_zh": "添加节点的子节点（不覆盖原有子节点值）"}, {"name": "addElementsText", "comment": "* 添加当前节点的文本（不覆盖原有文本）\n   * @param text 文本", "body": "public addElementsText(text: string): void{\n    let objKeys: string[] = Object.keys(this._formatResultObj);\n    let indexOfKey = objKeys.indexOf(SVGAttrConstants.ATTR_KEY_ELEMENTS);\n    this._textObj[SVGAttrConstants.ATTR_KEY_TYPE] = SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT;\n    this._textObj[SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT] = text;\n    if (indexOfKey === -1) {\n      this._elements = new Array();\n      this._elements.push(this._textObj);\n      this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = this._elements;\n    } else {\n      let elementsArray = this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS];\n      if (isArray(elementsArray)) {\n        elementsArray.push(this._textObj);\n        this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = elementsArray;\n      }\n    }\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 3, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 2, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 2, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 3.75, "Consistency": 4, "Fluency": 4, "Relevance": 3}, "comment_zh": "添加当前节点的文本（不覆盖原有文本）"}], "token_count": 1284, "content_with_no_comment": "\n\nimport {ArrayTypeRange} from \"../util/ObjOrArrayUtil\";\nimport SVGAttrConstants from \"../constants/SVGAttrConstants\"\nimport {isArray} from '../tools/IsArrayFunction'\n\nexport class SVGSpecifiedFormat {\n  \n  private _formatResultObj: Record<string,string|object> = {};\n  private _textObj: Record<string,string> = {};\n  private _elements: Array<Object>=[] ;\n\n\n  \n  public setElementType(elementType: string): void{\n    let index = ArrayTypeRange.indexOf(elementType);\n    if (index === -1) {\n      return;\n    }\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_TYPE] = elementType;\n  }\n\n  \n  public getElementType(): string{\n    return this._formatResultObj[SVGAttrConstants.ATTR_KEY_TYPE] as string;\n  }\n\n  \n  public setElementName(elementName: string): void {\n    if (!elementName) {\n      return;\n    }\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_NAME] = elementName;\n  }\n\n  \n  public getElementName(): string{\n    return this._formatResultObj[SVGAttrConstants.ATTR_KEY_NAME]as string;\n  }\n\n  \n  public setAttributes(attributes: Object): void {\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_ATTRIBUTES] = attributes;\n  }\n\n  \n  public getAttributes(): object{\n    return this._formatResultObj[SVGAttrConstants.ATTR_KEY_ATTRIBUTES]as  object;\n  }\n\n  \n  public setElements(elementsInfo: Object): void{\n    if (isArray(elementsInfo)) {\n      this._elements = elementsInfo;\n    } else {\n      this._elements = new Array()\n      this._elements.push(elementsInfo);\n    }\n\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = this._elements;\n  }\n\n  \n  public addElements(elements: Object): void{\n    let objKeys: string[] = Object.keys(this._formatResultObj);\n    let indexOfKey = objKeys.indexOf(SVGAttrConstants.ATTR_KEY_ELEMENTS);\n    if (indexOfKey === -1) {\n      this._elements = new Array();\n      if (isArray(elements)) {\n        this._elements.concat(elements);\n      } else {\n        this._elements.push(elements);\n      }\n      this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = this._elements;\n    } else {\n      let elementsArray = this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS];\n      if (isArray(elementsArray)) {\n        if (isArray(elements)) {\n          elementsArray.concat(elements);\n        } else {\n          elementsArray.push(elements);\n        }\n        this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = elementsArray;\n      }\n    }\n  }\n\n  \n  public getElements(): object{\n    return this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS]as object;\n  }\n\n  \n  public setElementsText(text: string): void{\n    this._elements = new Array();\n    this._textObj[SVGAttrConstants.ATTR_KEY_TYPE] = SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT;\n    this._textObj[SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT] = text;\n    this._elements.push(this._textObj);\n    this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = this._elements;\n  }\n\n  \n  public addElementsText(text: string): void{\n    let objKeys: string[] = Object.keys(this._formatResultObj);\n    let indexOfKey = objKeys.indexOf(SVGAttrConstants.ATTR_KEY_ELEMENTS);\n    this._textObj[SVGAttrConstants.ATTR_KEY_TYPE] = SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT;\n    this._textObj[SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT] = text;\n    if (indexOfKey === -1) {\n      this._elements = new Array();\n      this._elements.push(this._textObj);\n      this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = this._elements;\n    } else {\n      let elementsArray = this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS];\n      if (isArray(elementsArray)) {\n        elementsArray.push(this._textObj);\n        this._formatResultObj[SVGAttrConstants.ATTR_KEY_ELEMENTS] = elementsArray;\n      }\n    }\n  }\n\n  \n  public toObj(): object{\n    return this._formatResultObj;\n  }\n}\n"}
{"content": "/*\n * Copyright (c) 2023 Huawei Device Co., Ltd.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport worker, { MessageEvents } from '@ohos.worker';\nimport ArrayList from '@ohos.util.ArrayList';\nimport { Log } from '../../utils/Log';\nimport { Constants } from './Constants';\n\n\nconst TAG = 'WorkerThreadPool'\n\n// 跨模块调用worker路径前缀\nconst crossModulePathPrefix: string = '@bundle:com.ohos.photos/';\nconst PHONE_ENTRY_NAME: string = 'phone_photos';\nconst PC_ENTRY_NAME: string = 'pc_photos';\n\n/**\n * 自定义worker线程池，负责worker线程的创建、启动、销毁\n */\nexport class WorkerThreadPool {\n  public static readonly CAPACITY: number = 3;\n  private static workerThreadPoolInstance: WorkerThreadPool;\n  private workerList: ArrayList<worker.ThreadWorker> = new ArrayList();\n\n  private constructor() {\n  }\n\n  public static getInstance(): WorkerThreadPool {\n    if (!this.workerThreadPoolInstance) {\n      Log.info(TAG, 'single instance create')\n      this.workerThreadPoolInstance = new WorkerThreadPool();\n    }\n    return this.workerThreadPoolInstance;\n  }\n\n  /**\n   * 创建worker\n   * @param relativePath  worker.ts文件所在的相对路径\n   * @param deviceType    设备类型，对应不同entryName (phone, pc)\n   */\n  public static createWorker(relativePath: string, deviceType: string): worker.ThreadWorker {\n    Log.info(TAG, 'create worker, relativePath: ' + relativePath);\n    let entryName = deviceType == Constants.DEFAULT_DEVICE_TYPE ? PHONE_ENTRY_NAME : PC_ENTRY_NAME;\n    let workerInstance: worker.ThreadWorker;\n    try {\n      workerInstance = new worker.ThreadWorker(crossModulePathPrefix + entryName + relativePath);\n    } catch (err) {\n      Log.error(TAG, 'create worker instance failed' + err);\n    }\n    return workerInstance;\n  }\n\n  /**\n   * 启动一个worker\n   * @param worker  worker对象\n   * @param buffer  传入worker的buffer\n   * @param excFunc 主线程回调函数\n   * @param name    worker名称\n   */\n  public static startWorker(worker: worker.ThreadWorker, buffer: ArrayBuffer, callback: Function, name: string): void {\n    if (!worker) {\n      Log.error(TAG, 'worker' + name + 'is null');\n    }\n    worker.postMessage(buffer);\n    worker.onmessage = function (e: MessageEvents) {\n      callback(e, name);\n      Log.info(TAG, 'worker onmessage end, terminate')\n      worker.terminate();\n    }\n    worker.onexit = function () {\n      Log.debug(TAG, 'worker' + name + 'exit');\n    }\n  }\n\n  /**\n   * 终止worker运行\n   * @param worker\n   */\n  public static terminateWorker(worker: worker.ThreadWorker) {\n    Log.info(TAG, 'terminate worker')\n    if (!worker) {\n      Log.error(TAG, 'worker is null');\n    }\n    try {\n      worker.terminate();\n    } catch (err) {\n      Log.error(TAG, 'worker terminate error: ' + JSON.stringify(err));\n    }\n  }\n\n  /**\n   * 最大容量\n   */\n  public capacity(): number {\n    return WorkerThreadPool.CAPACITY;\n  }\n\n  /**\n   * 执行workerList中的worker\n   * @param path                   worker.ts文件路径\n   * @param data                   处理的数据\n   * @param byteLengthOfOneGroup   需要以组为单位处理的数据，例如直方图的RGBA像素统计，一组4个字节，该值为4\n   * @param excFunc                主线程回调函数，用于worker结果的数据汇总\n   */\n  public run(path: string, data: ArrayBuffer, byteLengthOfOneGroup: number, mainThreadCallback: Function) {\n    Log.info(TAG, 'thread pool start running, capacity: ' + WorkerThreadPool.CAPACITY);\n    let groupCount = data.byteLength / byteLengthOfOneGroup;\n    let byteLengthOfOneWorker = Math.floor(groupCount / WorkerThreadPool.CAPACITY) * byteLengthOfOneGroup;\n    let deviceType: string = AppStorage.get('deviceType');\n    const FIRST_INDEX = 0;\n    for (let i = WorkerThreadPool.CAPACITY - 1; i >= 0; i--) {\n      let workerInstance = WorkerThreadPool.createWorker(path, deviceType);\n      this.workerList.add(workerInstance);\n      let dataSlice = (i == FIRST_INDEX) ? data.slice(i * byteLengthOfOneWorker) : data.slice(i *\n      byteLengthOfOneWorker, (i + 1) *\n      byteLengthOfOneWorker);\n      WorkerThreadPool.startWorker(workerInstance, dataSlice, mainThreadCallback, 'worker instance ' + i);\n    }\n  }\n\n  /**\n   * 终止WorkerThreadPool的所有worker\n   */\n  public stop(): void {\n    this.workerList.forEach((workerInstance, index) => {\n      Log.info(TAG, 'worker ' + index + ' terminate.')\n      WorkerThreadPool.terminateWorker(workerInstance);\n    });\n    this.workerList.clear();\n  }\n}", "filepath": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs_nocomments/applications_photos/common/src/main/ets/default/model/common/WorkerThreadPool.ets", "repo": "applications_photos", "isArkUI": false, "functions": [{"name": "stop", "comment": "* 终止WorkerThreadPool的所有worker", "body": "public stop(): void {\n    this.workerList.forEach((workerInstance, index) => {\n      Log.info(TAG, 'worker ' + index + ' terminate.')\n      WorkerThreadPool.terminateWorker(workerInstance);\n    });\n    this.workerList.clear();\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 2, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 3}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 4, "Relevance": 3.25}, "comment_zh": "终止WorkerThreadPool的所有工作线程"}], "token_count": 1193, "content_with_no_comment": "\nimport worker, { MessageEvents } from '@ohos.worker';\nimport ArrayList from '@ohos.util.ArrayList';\nimport { Log } from '../../utils/Log';\nimport { Constants } from './Constants';\n\n\nconst TAG = 'WorkerThreadPool'\n\n\nconst crossModulePathPrefix: string = '@bundle:com.ohos.photos/';\nconst PHONE_ENTRY_NAME: string = 'phone_photos';\nconst PC_ENTRY_NAME: string = 'pc_photos';\n\n\nexport class WorkerThreadPool {\n  public static readonly CAPACITY: number = 3;\n  private static workerThreadPoolInstance: WorkerThreadPool;\n  private workerList: ArrayList<worker.ThreadWorker> = new ArrayList();\n\n  private constructor() {\n  }\n\n  public static getInstance(): WorkerThreadPool {\n    if (!this.workerThreadPoolInstance) {\n      Log.info(TAG, 'single instance create')\n      this.workerThreadPoolInstance = new WorkerThreadPool();\n    }\n    return this.workerThreadPoolInstance;\n  }\n\n  \n  public static createWorker(relativePath: string, deviceType: string): worker.ThreadWorker {\n    Log.info(TAG, 'create worker, relativePath: ' + relativePath);\n    let entryName = deviceType == Constants.DEFAULT_DEVICE_TYPE ? PHONE_ENTRY_NAME : PC_ENTRY_NAME;\n    let workerInstance: worker.ThreadWorker;\n    try {\n      workerInstance = new worker.ThreadWorker(crossModulePathPrefix + entryName + relativePath);\n    } catch (err) {\n      Log.error(TAG, 'create worker instance failed' + err);\n    }\n    return workerInstance;\n  }\n\n  \n  public static startWorker(worker: worker.ThreadWorker, buffer: ArrayBuffer, callback: Function, name: string): void {\n    if (!worker) {\n      Log.error(TAG, 'worker' + name + 'is null');\n    }\n    worker.postMessage(buffer);\n    worker.onmessage = function (e: MessageEvents) {\n      callback(e, name);\n      Log.info(TAG, 'worker onmessage end, terminate')\n      worker.terminate();\n    }\n    worker.onexit = function () {\n      Log.debug(TAG, 'worker' + name + 'exit');\n    }\n  }\n\n  \n  public static terminateWorker(worker: worker.ThreadWorker) {\n    Log.info(TAG, 'terminate worker')\n    if (!worker) {\n      Log.error(TAG, 'worker is null');\n    }\n    try {\n      worker.terminate();\n    } catch (err) {\n      Log.error(TAG, 'worker terminate error: ' + JSON.stringify(err));\n    }\n  }\n\n  \n  public capacity(): number {\n    return WorkerThreadPool.CAPACITY;\n  }\n\n  \n  public run(path: string, data: ArrayBuffer, byteLengthOfOneGroup: number, mainThreadCallback: Function) {\n    Log.info(TAG, 'thread pool start running, capacity: ' + WorkerThreadPool.CAPACITY);\n    let groupCount = data.byteLength / byteLengthOfOneGroup;\n    let byteLengthOfOneWorker = Math.floor(groupCount / WorkerThreadPool.CAPACITY) * byteLengthOfOneGroup;\n    let deviceType: string = AppStorage.get('deviceType');\n    const FIRST_INDEX = 0;\n    for (let i = WorkerThreadPool.CAPACITY - 1; i >= 0; i--) {\n      let workerInstance = WorkerThreadPool.createWorker(path, deviceType);\n      this.workerList.add(workerInstance);\n      let dataSlice = (i == FIRST_INDEX) ? data.slice(i * byteLengthOfOneWorker) : data.slice(i *\n      byteLengthOfOneWorker, (i + 1) *\n      byteLengthOfOneWorker);\n      WorkerThreadPool.startWorker(workerInstance, dataSlice, mainThreadCallback, 'worker instance ' + i);\n    }\n  }\n\n  \n  public stop(): void {\n    this.workerList.forEach((workerInstance, index) => {\n      Log.info(TAG, 'worker ' + index + ' terminate.')\n      WorkerThreadPool.terminateWorker(workerInstance);\n    });\n    this.workerList.clear();\n  }\n}"}
{"content": "/**\n * Copyright (C) 2022 Huawei Device Co., Ltd.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport fileio from '@ohos.fileio';\nimport xml from '@ohos.convertxml';\nimport SVGAttrConstants from './constants/SVGAttrConstants'\nimport { SVGDeclares } from './svggen/SVGDeclares';\nimport { checkElements, hasKeyInObj } from './util/ObjOrArrayUtil';\nimport { consoleInfo } from './util/LogUtil';\nimport { SVGXMLChecker } from './SVGXMLChecker'\nimport { GlobalContext } from './tools/GlobalContext';\nimport { isArray } from './tools/IsArrayFunction'\nimport { getKeys } from './tools/GetKeysTest'\nimport { objCreate } from './tools/ObjCreate'\nimport {deleteProperty}from'./tools/DeleteProperty'\n\n/**\n * SVG管理器\n */\nexport class SVGManager {\n  private static _sInstance: SVGManager;\n  private _svgObj: Record<string, object>|object;\n  private _filePath: string = '';\n  private _checker: SVGXMLChecker;\n\n  public static getInstance(): SVGManager{\n    if (!SVGManager._sInstance) {\n      SVGManager._sInstance = new SVGManager();\n    }\n    return SVGManager._sInstance;\n  }\n\n  /**\n   * 构造函数\n   */\n  private constructor() {\n    // 实现单例，私有构造方法\n    this._svgObj = objCreate;\n    this._checker = new SVGXMLChecker();\n    let filesDir: string = GlobalContext.getContext().getObject(\"filesDir\") as string\n    this._filePath = filesDir;\n  }\n\n  /**\n   * 获取整个SVG的整体对象\n   */\n  public getSVGTotalObj() {\n    return this._svgObj;\n  }\n\n  /**\n   * 创建SVG的头部声明\n   */\n  public createSVGDeclares(): Object{\n    // 为整个SVG文档添加设置标准SVG声明\n    let declares = new SVGDeclares();\n    declares.setXMLVersion('1.0');\n    declares.setEncoding('utf-8');\n    declares.setStandalone(true);\n    this._svgObj = declares.toObj();\n    return this._svgObj;\n  }\n\n  /**\n   * 获取SVG标签对应的对象\n   *\n   * 找到标签内容为svg的对象\n   * @param obj svg文件对应的对象\n   * @return 返回svg标签对应的JS对象，如果没找到，返回false\n   */\n  public getSVGRoot(obj: Object = this._svgObj): object{\n    let objValue: Record<string, object>=obj as Record<string, object>\n    let svgObj: Record<string, string | object> = {};\n\n    // 获取节点信息对象\n    let svgElements: object | Array<object> = objValue[SVGAttrConstants.ATTR_KEY_ELEMENTS];\n    if (typeof svgElements !== SVGAttrConstants.TYPEOF_OBJECT || !isArray(svgElements)) {\n      return svgObj;\n    }\n    svgElements.forEach((nodeObj: Record<string, string | object>) => {\n      if (typeof nodeObj !== SVGAttrConstants.TYPEOF_OBJECT) {\n        return;\n      }\n      let objKeys: string[] = getKeys(nodeObj);\n      objKeys.forEach((svgObjKey) => {\n        if (svgObjKey === SVGAttrConstants.ATTR_KEY_NAME && nodeObj[svgObjKey] === 'svg') {\n          svgObj = nodeObj;\n          return;\n        }\n      })\n    })\n    return svgObj;\n  }\n\n  /**\n   * 为指定对象添加子节点\n   * @param parentObj 要添加子节点的对象\n   * @param childPropertyValue 对应主键的值\n   */\n  public addChildNode(parentObj: Object, childPropertyValue: Object): boolean{\n    let parentObjValue: Record<string, Array<object>>|object=parentObj as Record<string, Array<object>>|object\n    if (!checkElements(childPropertyValue)) {\n      throw Error('node keys must in [`type`, `text`] or [`type`, `name`, `attributes`, `elements`]');\n      return false;\n    }\n    if (hasKeyInObj(parentObjValue, SVGAttrConstants.ATTR_KEY_ELEMENTS)\n      && isArray(parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS])) {\n      parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS].push(childPropertyValue);\n    } else if (!hasKeyInObj(parentObjValue, SVGAttrConstants.ATTR_KEY_ELEMENTS)) {\n      let elementsArray: Array<object> = [];\n      elementsArray.push(childPropertyValue);\n      parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] = elementsArray;\n    }\n    return true;\n  }\n\n  /**\n   * 为某个节点设置子节点（可能会覆盖原有值）\n   * @param parentObj 父节点\n   * @param childPropertyValue 子节点\n   * @return 返回是否设置添加子节点\n   */\n  public setChildNode(parentObj: Object, childPropertyValue: Object): boolean{\n    let parentObjValue: Record<string, Array<object>|object>|object=parentObj as Record<string, Array<object>|object>|object\n    if (!checkElements(childPropertyValue)) {\n      throw Error('node keys must in [`type`, `text`] or [`type`, `name`, `attributes`, `elements`]');\n      return false;\n    }\n\n    if (typeof childPropertyValue === SVGAttrConstants.TYPEOF_OBJECT) {\n      if (isArray(childPropertyValue)) {\n        parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] = childPropertyValue;\n      } else {\n        let elementsArray: Array<object> = [];\n        elementsArray.push(childPropertyValue);\n        parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] = elementsArray;\n      }\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * 保存SVG文件\n   * @param fileName svg文件名称：svg.svg\n   * @param fileContent SVG文件的路径/内容\n   */\n  public saveSVG(fileName: string, fileContent: string | Object, onSuccess?: () => void, onFailed?: (number: number, Error: Error) => void): void {\n    if (!fileName || fileName.length < 5 || fileName.substr(fileName.length - 4) !== '.svg') {\n      if (!!onFailed) {\n        onFailed(0, new Error('the suffix of SVG file must be .svg'));\n      }\n      return;\n    }\n\n    let fileXMLInfo = '';\n    if (typeof fileContent === 'object' && !isArray(fileContent)) {\n      let fileContentText: string = this.convertObjToXML(fileContent) as string;\n      fileXMLInfo = fileContentText;\n    } else if (typeof fileContent === 'string') {\n      fileXMLInfo = fileContent;\n    }\n\n    let that = this;\n    if (fileXMLInfo) {\n      try {\n        this._checker.check(fileXMLInfo, () => {\n          let newFileName = '';\n          let pathArray: string[] = fileName.split('/');\n          if (pathArray.length >= 2) {\n            let folderArray = pathArray.splice(0, pathArray.length - 1);\n            folderArray.forEach((folderName) => {\n              if (folderName) {\n                newFileName += folderName;\n                that.createFolder(that._filePath + '/' + newFileName);\n                newFileName += '/';\n              }\n            })\n            newFileName += pathArray.splice(pathArray.length - 1);\n          } else {\n            newFileName = fileName;\n          }\n          let svgURI = that._filePath + '/' + newFileName;\n          that._createFile(svgURI);\n          that._writeFile(svgURI, fileXMLInfo);\n          if (!!onSuccess) {\n            onSuccess();\n          }\n\n        })\n      } catch (e) {\n        consoleInfo('SVGManager saveFile checker failed ', e)\n        if (!!onFailed) {\n          onFailed(1, e);\n        }\n\n      }\n    }\n  }\n\n  /**\n   * 转换数据为XML格式\n   * @param fileContentObj 要被转换为XML格式的对象\n   * @return xml格式数据\n   */\n  public convertObjToXML(fileContentObj: Object): string|null{\n    let  fileContentObjValue: Record<string, string | object>|object=fileContentObj\n    if (typeof fileContentObj === SVGAttrConstants.TYPEOF_OBJECT) {\n      let xmlResult = '';\n      let objKeys: string[] = getKeys(fileContentObjValue);\n      objKeys.forEach((element) => {\n        if (element === SVGAttrConstants.ATTR_KEY_DECLARATION) {\n          xmlResult += this._convertDeclaration(fileContentObjValue[element] as  Record<string, object>);\n        } else if (element === SVGAttrConstants.ATTR_KEY_ELEMENTS) {\n          xmlResult += this._convertArrayToXML(fileContentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] as object);\n        }\n      })\n      return xmlResult;\n    }\n    return null;\n  }\n\n  /**\n   * 转换数组为XML格式\n   * @param arrayObj 要被转换为XML格式的数组对象\n   * @return xml格式数据\n   */\n  private _convertArrayToXML(arrayObj: object): string {\n    let arrayXml = '';\n    if (typeof arrayObj === SVGAttrConstants.TYPEOF_OBJECT && isArray(arrayObj)) {\n      arrayObj.forEach((element: Record<string, string>) => {\n        if (this._convertObjToXML(element)) {\n          arrayXml += this._convertObjToXML(element);\n        }\n      });\n    }\n    return arrayXml;\n  }\n\n  /**\n   * 转换Obj数据为XML格式\n   * @param contentObj 要被转换为XML格式的对象\n   * @return xml格式数据\n   */\n  private _convertObjToXML(contentObj: Object): string | undefined {\n    let contentObjValue: Record<string, string | object > =contentObj as Record<string, string | object >\n    let objXml = '';\n    if (typeof contentObjValue !== SVGAttrConstants.TYPEOF_OBJECT || !hasKeyInObj(contentObjValue, SVGAttrConstants.ATTR_KEY_TYPE)) {\n      return objXml;\n    }\n\n    let elementType: string = contentObjValue[SVGAttrConstants.ATTR_KEY_TYPE] as string;\n    if (elementType === SVGAttrConstants.ATTR_VALUE_ELEMENT) {\n      objXml = '<'\n    }\n\n    if (hasKeyInObj(contentObjValue, SVGAttrConstants.ATTR_KEY_NAME)) {\n      objXml += contentObjValue[SVGAttrConstants.ATTR_KEY_NAME] + \" \";\n    }\n\n    if (hasKeyInObj(contentObjValue, SVGAttrConstants.ATTR_KEY_ATTRIBUTES)) {\n      let attrObj = contentObjValue[SVGAttrConstants.ATTR_KEY_ATTRIBUTES] as object;\n      if (typeof attrObj !== SVGAttrConstants.TYPEOF_OBJECT) {\n        return;\n      }\n      let objKeys: string[] = getKeys(attrObj);\n      objKeys.forEach((attrKey) => {\n        if (typeof attrObj[attrKey] === SVGAttrConstants.TYPEOF_OBJECT) {\n          let viewBoxObj: object = attrObj[attrKey];\n          objXml += attrKey + \"=\\\"\";\n          let objKeys: string[] = getKeys(viewBoxObj);\n          objKeys.forEach((key) => {\n            objXml += viewBoxObj[key] + ' ';\n          })\n          objXml += \"\\\"\";\n        } else {\n          objXml += this._parseGeneralTypeToXML(attrKey, attrObj[attrKey]);\n        }\n      })\n    }\n\n    if (elementType === SVGAttrConstants.ATTR_VALUE_ELEMENT) {\n      if (hasKeyInObj(contentObjValue, SVGAttrConstants.ATTR_KEY_ELEMENTS)) {\n        objXml += '>';\n        objXml += this._convertArrayToXML(contentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] as object);\n        objXml += '</' + contentObjValue[SVGAttrConstants.ATTR_KEY_NAME] + '>';\n      } else {\n        objXml += '/>';\n      }\n    } else if (elementType === SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT) {\n      objXml += contentObjValue[SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT];\n    }\n\n    return objXml;\n  }\n\n  /**\n   * 转换声明数据为XML格式\n   * @param declarationObj 要被转换为XML格式的对象\n   * @return xml格式数据\n   */\n  private _convertDeclaration(declarationObj:Object): string {\n    let declarationObjValue: Record<string, object>=declarationObj as Record<string, object>\n    let declarationString = '<?xml ';\n    if (hasKeyInObj(declarationObjValue, SVGAttrConstants.ATTR_KEY_ATTRIBUTES)) {\n      let declarationAttrObj: object = declarationObjValue[SVGAttrConstants.ATTR_KEY_ATTRIBUTES] as object;\n      let objKeys: string[] = getKeys(declarationAttrObj);\n      objKeys.forEach((element) => {\n        declarationString += this._parseGeneralTypeToXML(element, declarationAttrObj[element]);\n      })\n    }\n    declarationString += '?>';\n    return declarationString;\n  }\n\n  /**\n   * 转换键值对数据为XML格式\n   * @param key 主键\n   * @param generalObj 值\n   */\n  private _parseGeneralTypeToXML(key: string, value: number | string): string {\n    let xml = key + \"=\\\"\" + value + \"\\\" \";\n    return xml;\n  }\n\n  /**\n   * 获取SVG标签对应的对象/值\n   * @param parentObj 整个XML对应的对象\n   * @param key  主键\n   * @return false 或 SVG根标签对应的对象\n   */\n  public getValueForKey(parentObj: Object, key: string): boolean|object|string {\n    let parentObjValue: Record<string,object>|object=parentObj\n    let svgIndex = hasKeyInObj(parentObjValue, key);\n    if (svgIndex) {\n      return parentObjValue[key];\n    }\n    return false;\n  }\n\n  /**\n   * 移除子节点/属性\n   * @param parentObj 将要删除节点或属性的对象\n   * @param key 将要删除的节点或属性对应的key值\n   */\n  public removeByKey(parentObj: Object, key: string): void {\n    let parentObjValue: Record<string,object>|object=parentObj\n    let svgIndex = hasKeyInObj(parentObjValue, key);\n    if (svgIndex) {\n      deleteProperty(parentObjValue,key)\n    }\n  }\n\n  /**\n   * 为对象设置属性或子节点（覆盖原有键值对）\n   * @param parentObj 要设置属性或结点的对象\n   * @param key 主键\n   * @param value 属性/节点\n   */\n  public setAttribute(parentObj: Object, key: string, value: string): void {\n    let  parentObjValue: Record<string,string|object>|object=parentObj\n    if (typeof parentObjValue === SVGAttrConstants.TYPEOF_OBJECT && key && value) {\n      let attributesObj: Record<string,string|object>|string = '' ;\n      if (hasKeyInObj(parentObjValue, SVGAttrConstants.ATTR_KEY_ATTRIBUTES)) {\n        attributesObj = parentObjValue[SVGAttrConstants.ATTR_KEY_ATTRIBUTES]as Record<string,string|object> ;\n      } else {\n        attributesObj = {  };\n      }\n      attributesObj[key] = value;\n    }\n    parentObjValue[key] = value;\n  }\n\n  /**\n   * 创建文件夹\n   * @param 文件夹绝对路径\n   */\n  public createFolder(path: string): void {\n    //创建文件夹\n    if (!this._isFolderExisted(path)) {\n      fileio.mkdirSync(path);\n    }\n  }\n\n  /**\n   * 判断文件夹是否存在\n   * @param 文件夹绝对路径\n   */\n  public _isFolderExisted(path: string): boolean{\n    try {\n      let stat = fileio.statSync(path)\n      return stat.isDirectory()\n    } catch (e) {\n      consoleInfo('SVGManager existFolder', e.message);\n      return false\n    }\n  }\n\n  /**\n   * 新建文件\n   * @param path 文件绝对路径及文件名\n   */\n  private _createFile(path: string): number {\n    return fileio.openSync(path, 0o100, 0o666);\n  }\n\n  /**\n   * 向path写入content数据，覆盖旧数据\n   * @param path 文件路径\n   * @param content 文件内容\n   */\n  private _writeFile(path: string, content: ArrayBuffer | string): void{\n    try {\n      let fd = fileio.openSync(path, 0o102, 0o666);\n      fileio.ftruncateSync(fd);\n      fileio.writeSync(fd, content);\n      fileio.fsyncSync(fd);\n      fileio.closeSync(fd);\n    } catch (e) {\n      consoleInfo('SVGManager writeFile ', 'Failed to writeFile for ' + e);\n    }\n  }\n\n  /**\n   * 获取data路径\n   * @param onSuccess 路径数据回调\n   */\n  public getFilePath(onSuccess: (filesDir: string) => void): void {\n    let filesDir: string = GlobalContext.getContext().getObject(\"filesDir\") as string\n    onSuccess(filesDir);\n  }\n\n  /**\n   * 解析SVG文件\n   * @param fileName 文件路径/名称\n   * @param onSuccess 解析成功回调\n   * @param onFailed 解析失败回调\n   */\n  public parse(fileName: string, onSuccess: (result: string) => void, onFailed?: (error: Error) => void): void {\n    // File绝对路径不能有`file://`\n    let fileCompletePath = this._filePath + '/' + fileName;\n    let that = this;\n    fileio.readText(fileCompletePath)\n      .then( (str)=> {\n        try {\n          that._checker.check(str, ()=> {\n            let xmlInstance = new xml.ConvertXML();\n            let xmlResult = xmlInstance.convert(str, {\n              trim: false,\n              declarationKey: 'declaration',\n              instructionKey: 'instruction',\n              attributesKey: 'attributes',\n              textKey: 'text',\n              cdataKey: 'cdata',\n              doctypeKey: 'doctype',\n              commentKey: 'comment',\n              parentKey: 'parent',\n              typeKey: 'type',\n              nameKey: 'name',\n              elementsKey: 'elements'\n            });\n            if (xmlResult) {\n              if (typeof xmlResult === 'object') {\n                onSuccess(JSON.stringify(xmlResult));\n              } else {\n                onSuccess(xmlResult);\n              }\n            } else {\n              consoleInfo('SVGManager parse failed ', 'result is empty');\n              if (!!onFailed) {\n                onFailed(new Error('SVGManager parse failed, result is empty'));\n              }\n\n            }\n          })\n        } catch (e) {\n          consoleInfo('SVGManager parse checker failed ', e)\n          if (!!onFailed) {\n            onFailed(e);\n          }\n        }\n      }).catch((e: string|Error) => {\n      consoleInfo('SVGManager parse failed catch ', e as string);\n      if (!!onFailed) {\n        onFailed(e as Error);\n      }\n    })\n  }\n}", "filepath": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs_nocomments/XmlGraphicsBatik/library/src/main/ets/batik/SVGManager.ets", "repo": "XmlGraphicsBatik", "isArkUI": false, "functions": [{"name": "createSVGDeclares", "comment": "* 创建SVG的头部声明", "body": "public createSVGDeclares(): Object{\n    // 为整个SVG文档添加设置标准SVG声明\n    let declares = new SVGDeclares();\n    declares.setXMLVersion('1.0');\n    declares.setEncoding('utf-8');\n    declares.setStandalone(true);\n    this._svgObj = declares.toObj();\n    return this._svgObj;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 0, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 3, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 2.75, "Relevance": 4}, "comment_zh": "创建SVG的头部声明"}, {"name": "setChildNode", "comment": "* 为某个节点设置子节点（可能会覆盖原有值）\n   * @param parentObj 父节点\n   * @param childPropertyValue 子节点\n   * @return 返回是否设置添加子节点", "body": "public setChildNode(parentObj: Object, childPropertyValue: Object): boolean{\n    let parentObjValue: Record<string, Array<object>|object>|object=parentObj as Record<string, Array<object>|object>|object\n    if (!checkElements(childPropertyValue)) {\n      throw Error('node keys must in [`type`, `text`] or [`type`, `name`, `attributes`, `elements`]');\n      return false;\n    }\n\n    if (typeof childPropertyValue === SVGAttrConstants.TYPEOF_OBJECT) {\n      if (isArray(childPropertyValue)) {\n        parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] = childPropertyValue;\n      } else {\n        let elementsArray: Array<object> = [];\n        elementsArray.push(childPropertyValue);\n        parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] = elementsArray;\n      }\n      return true;\n    }\n    return false;\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 2, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 2, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 3, "Code Editor_Relevance": 3, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 3}, "criterion_averages": {"Coherence": 3.5, "Consistency": 4, "Fluency": 3.75, "Relevance": 3}, "comment_zh": "为某个节点设置子节点（可能会覆盖原有值），返回是否成功添加子节点"}, {"name": "_isFolderExisted", "comment": "* 判断文件夹是否存在\n   * @param 文件夹绝对路径", "body": "public _isFolderExisted(path: string): boolean{\n    try {\n      let stat = fileio.statSync(path)\n      return stat.isDirectory()\n    } catch (e) {\n      consoleInfo('SVGManager existFolder', e.message);\n      return false\n    }\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 2, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 0, "Code Reviewer_Relevance": 2, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 3, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 2}, "criterion_averages": {"Coherence": 3.5, "Consistency": 3.75, "Fluency": 3, "Relevance": 3}, "comment_zh": "判断文件夹是否存在"}, {"name": "_writeFile", "comment": "* 向path写入content数据，覆盖旧数据\n   * @param path 文件路径\n   * @param content 文件内容", "body": "private _writeFile(path: string, content: ArrayBuffer | string): void{\n    try {\n      let fd = fileio.openSync(path, 0o102, 0o666);\n      fileio.ftruncateSync(fd);\n      fileio.writeSync(fd, content);\n      fileio.fsyncSync(fd);\n      fileio.closeSync(fd);\n    } catch (e) {\n      consoleInfo('SVGManager writeFile ', 'Failed to writeFile for ' + e);\n    }\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 4, "Original Code Author_Relevance": 4, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 4, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 4, "Systems Analyst_Relevance": 3}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 4, "Relevance": 3.75}, "comment_zh": "向path写入content数据，覆盖旧数据"}], "token_count": 4148, "content_with_no_comment": "\n\nimport fileio from '@ohos.fileio';\nimport xml from '@ohos.convertxml';\nimport SVGAttrConstants from './constants/SVGAttrConstants'\nimport { SVGDeclares } from './svggen/SVGDeclares';\nimport { checkElements, hasKeyInObj } from './util/ObjOrArrayUtil';\nimport { consoleInfo } from './util/LogUtil';\nimport { SVGXMLChecker } from './SVGXMLChecker'\nimport { GlobalContext } from './tools/GlobalContext';\nimport { isArray } from './tools/IsArrayFunction'\nimport { getKeys } from './tools/GetKeysTest'\nimport { objCreate } from './tools/ObjCreate'\nimport {deleteProperty}from'./tools/DeleteProperty'\n\n\nexport class SVGManager {\n  private static _sInstance: SVGManager;\n  private _svgObj: Record<string, object>|object;\n  private _filePath: string = '';\n  private _checker: SVGXMLChecker;\n\n  public static getInstance(): SVGManager{\n    if (!SVGManager._sInstance) {\n      SVGManager._sInstance = new SVGManager();\n    }\n    return SVGManager._sInstance;\n  }\n\n  \n  private constructor() {\n    \n    this._svgObj = objCreate;\n    this._checker = new SVGXMLChecker();\n    let filesDir: string = GlobalContext.getContext().getObject(\"filesDir\") as string\n    this._filePath = filesDir;\n  }\n\n  \n  public getSVGTotalObj() {\n    return this._svgObj;\n  }\n\n  \n  public createSVGDeclares(): Object{\n    \n    let declares = new SVGDeclares();\n    declares.setXMLVersion('1.0');\n    declares.setEncoding('utf-8');\n    declares.setStandalone(true);\n    this._svgObj = declares.toObj();\n    return this._svgObj;\n  }\n\n  \n  public getSVGRoot(obj: Object = this._svgObj): object{\n    let objValue: Record<string, object>=obj as Record<string, object>\n    let svgObj: Record<string, string | object> = {};\n\n    \n    let svgElements: object | Array<object> = objValue[SVGAttrConstants.ATTR_KEY_ELEMENTS];\n    if (typeof svgElements !== SVGAttrConstants.TYPEOF_OBJECT || !isArray(svgElements)) {\n      return svgObj;\n    }\n    svgElements.forEach((nodeObj: Record<string, string | object>) => {\n      if (typeof nodeObj !== SVGAttrConstants.TYPEOF_OBJECT) {\n        return;\n      }\n      let objKeys: string[] = getKeys(nodeObj);\n      objKeys.forEach((svgObjKey) => {\n        if (svgObjKey === SVGAttrConstants.ATTR_KEY_NAME && nodeObj[svgObjKey] === 'svg') {\n          svgObj = nodeObj;\n          return;\n        }\n      })\n    })\n    return svgObj;\n  }\n\n  \n  public addChildNode(parentObj: Object, childPropertyValue: Object): boolean{\n    let parentObjValue: Record<string, Array<object>>|object=parentObj as Record<string, Array<object>>|object\n    if (!checkElements(childPropertyValue)) {\n      throw Error('node keys must in [`type`, `text`] or [`type`, `name`, `attributes`, `elements`]');\n      return false;\n    }\n    if (hasKeyInObj(parentObjValue, SVGAttrConstants.ATTR_KEY_ELEMENTS)\n      && isArray(parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS])) {\n      parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS].push(childPropertyValue);\n    } else if (!hasKeyInObj(parentObjValue, SVGAttrConstants.ATTR_KEY_ELEMENTS)) {\n      let elementsArray: Array<object> = [];\n      elementsArray.push(childPropertyValue);\n      parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] = elementsArray;\n    }\n    return true;\n  }\n\n  \n  public setChildNode(parentObj: Object, childPropertyValue: Object): boolean{\n    let parentObjValue: Record<string, Array<object>|object>|object=parentObj as Record<string, Array<object>|object>|object\n    if (!checkElements(childPropertyValue)) {\n      throw Error('node keys must in [`type`, `text`] or [`type`, `name`, `attributes`, `elements`]');\n      return false;\n    }\n\n    if (typeof childPropertyValue === SVGAttrConstants.TYPEOF_OBJECT) {\n      if (isArray(childPropertyValue)) {\n        parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] = childPropertyValue;\n      } else {\n        let elementsArray: Array<object> = [];\n        elementsArray.push(childPropertyValue);\n        parentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] = elementsArray;\n      }\n      return true;\n    }\n    return false;\n  }\n\n  \n  public saveSVG(fileName: string, fileContent: string | Object, onSuccess?: () => void, onFailed?: (number: number, Error: Error) => void): void {\n    if (!fileName || fileName.length < 5 || fileName.substr(fileName.length - 4) !== '.svg') {\n      if (!!onFailed) {\n        onFailed(0, new Error('the suffix of SVG file must be .svg'));\n      }\n      return;\n    }\n\n    let fileXMLInfo = '';\n    if (typeof fileContent === 'object' && !isArray(fileContent)) {\n      let fileContentText: string = this.convertObjToXML(fileContent) as string;\n      fileXMLInfo = fileContentText;\n    } else if (typeof fileContent === 'string') {\n      fileXMLInfo = fileContent;\n    }\n\n    let that = this;\n    if (fileXMLInfo) {\n      try {\n        this._checker.check(fileXMLInfo, () => {\n          let newFileName = '';\n          let pathArray: string[] = fileName.split('/');\n          if (pathArray.length >= 2) {\n            let folderArray = pathArray.splice(0, pathArray.length - 1);\n            folderArray.forEach((folderName) => {\n              if (folderName) {\n                newFileName += folderName;\n                that.createFolder(that._filePath + '/' + newFileName);\n                newFileName += '/';\n              }\n            })\n            newFileName += pathArray.splice(pathArray.length - 1);\n          } else {\n            newFileName = fileName;\n          }\n          let svgURI = that._filePath + '/' + newFileName;\n          that._createFile(svgURI);\n          that._writeFile(svgURI, fileXMLInfo);\n          if (!!onSuccess) {\n            onSuccess();\n          }\n\n        })\n      } catch (e) {\n        consoleInfo('SVGManager saveFile checker failed ', e)\n        if (!!onFailed) {\n          onFailed(1, e);\n        }\n\n      }\n    }\n  }\n\n  \n  public convertObjToXML(fileContentObj: Object): string|null{\n    let  fileContentObjValue: Record<string, string | object>|object=fileContentObj\n    if (typeof fileContentObj === SVGAttrConstants.TYPEOF_OBJECT) {\n      let xmlResult = '';\n      let objKeys: string[] = getKeys(fileContentObjValue);\n      objKeys.forEach((element) => {\n        if (element === SVGAttrConstants.ATTR_KEY_DECLARATION) {\n          xmlResult += this._convertDeclaration(fileContentObjValue[element] as  Record<string, object>);\n        } else if (element === SVGAttrConstants.ATTR_KEY_ELEMENTS) {\n          xmlResult += this._convertArrayToXML(fileContentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] as object);\n        }\n      })\n      return xmlResult;\n    }\n    return null;\n  }\n\n  \n  private _convertArrayToXML(arrayObj: object): string {\n    let arrayXml = '';\n    if (typeof arrayObj === SVGAttrConstants.TYPEOF_OBJECT && isArray(arrayObj)) {\n      arrayObj.forEach((element: Record<string, string>) => {\n        if (this._convertObjToXML(element)) {\n          arrayXml += this._convertObjToXML(element);\n        }\n      });\n    }\n    return arrayXml;\n  }\n\n  \n  private _convertObjToXML(contentObj: Object): string | undefined {\n    let contentObjValue: Record<string, string | object > =contentObj as Record<string, string | object >\n    let objXml = '';\n    if (typeof contentObjValue !== SVGAttrConstants.TYPEOF_OBJECT || !hasKeyInObj(contentObjValue, SVGAttrConstants.ATTR_KEY_TYPE)) {\n      return objXml;\n    }\n\n    let elementType: string = contentObjValue[SVGAttrConstants.ATTR_KEY_TYPE] as string;\n    if (elementType === SVGAttrConstants.ATTR_VALUE_ELEMENT) {\n      objXml = '<'\n    }\n\n    if (hasKeyInObj(contentObjValue, SVGAttrConstants.ATTR_KEY_NAME)) {\n      objXml += contentObjValue[SVGAttrConstants.ATTR_KEY_NAME] + \" \";\n    }\n\n    if (hasKeyInObj(contentObjValue, SVGAttrConstants.ATTR_KEY_ATTRIBUTES)) {\n      let attrObj = contentObjValue[SVGAttrConstants.ATTR_KEY_ATTRIBUTES] as object;\n      if (typeof attrObj !== SVGAttrConstants.TYPEOF_OBJECT) {\n        return;\n      }\n      let objKeys: string[] = getKeys(attrObj);\n      objKeys.forEach((attrKey) => {\n        if (typeof attrObj[attrKey] === SVGAttrConstants.TYPEOF_OBJECT) {\n          let viewBoxObj: object = attrObj[attrKey];\n          objXml += attrKey + \"=\\\"\";\n          let objKeys: string[] = getKeys(viewBoxObj);\n          objKeys.forEach((key) => {\n            objXml += viewBoxObj[key] + ' ';\n          })\n          objXml += \"\\\"\";\n        } else {\n          objXml += this._parseGeneralTypeToXML(attrKey, attrObj[attrKey]);\n        }\n      })\n    }\n\n    if (elementType === SVGAttrConstants.ATTR_VALUE_ELEMENT) {\n      if (hasKeyInObj(contentObjValue, SVGAttrConstants.ATTR_KEY_ELEMENTS)) {\n        objXml += '>';\n        objXml += this._convertArrayToXML(contentObjValue[SVGAttrConstants.ATTR_KEY_ELEMENTS] as object);\n        objXml += '</' + contentObjValue[SVGAttrConstants.ATTR_KEY_NAME] + '>';\n      } else {\n        objXml += '/>';\n      }\n    } else if (elementType === SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT) {\n      objXml += contentObjValue[SVGAttrConstants.ATTR_KEY_OR_VALUE_TEXT];\n    }\n\n    return objXml;\n  }\n\n  \n  private _convertDeclaration(declarationObj:Object): string {\n    let declarationObjValue: Record<string, object>=declarationObj as Record<string, object>\n    let declarationString = '<?xml ';\n    if (hasKeyInObj(declarationObjValue, SVGAttrConstants.ATTR_KEY_ATTRIBUTES)) {\n      let declarationAttrObj: object = declarationObjValue[SVGAttrConstants.ATTR_KEY_ATTRIBUTES] as object;\n      let objKeys: string[] = getKeys(declarationAttrObj);\n      objKeys.forEach((element) => {\n        declarationString += this._parseGeneralTypeToXML(element, declarationAttrObj[element]);\n      })\n    }\n    declarationString += '?>';\n    return declarationString;\n  }\n\n  \n  private _parseGeneralTypeToXML(key: string, value: number | string): string {\n    let xml = key + \"=\\\"\" + value + \"\\\" \";\n    return xml;\n  }\n\n  \n  public getValueForKey(parentObj: Object, key: string): boolean|object|string {\n    let parentObjValue: Record<string,object>|object=parentObj\n    let svgIndex = hasKeyInObj(parentObjValue, key);\n    if (svgIndex) {\n      return parentObjValue[key];\n    }\n    return false;\n  }\n\n  \n  public removeByKey(parentObj: Object, key: string): void {\n    let parentObjValue: Record<string,object>|object=parentObj\n    let svgIndex = hasKeyInObj(parentObjValue, key);\n    if (svgIndex) {\n      deleteProperty(parentObjValue,key)\n    }\n  }\n\n  \n  public setAttribute(parentObj: Object, key: string, value: string): void {\n    let  parentObjValue: Record<string,string|object>|object=parentObj\n    if (typeof parentObjValue === SVGAttrConstants.TYPEOF_OBJECT && key && value) {\n      let attributesObj: Record<string,string|object>|string = '' ;\n      if (hasKeyInObj(parentObjValue, SVGAttrConstants.ATTR_KEY_ATTRIBUTES)) {\n        attributesObj = parentObjValue[SVGAttrConstants.ATTR_KEY_ATTRIBUTES]as Record<string,string|object> ;\n      } else {\n        attributesObj = {  };\n      }\n      attributesObj[key] = value;\n    }\n    parentObjValue[key] = value;\n  }\n\n  \n  public createFolder(path: string): void {\n    \n    if (!this._isFolderExisted(path)) {\n      fileio.mkdirSync(path);\n    }\n  }\n\n  \n  public _isFolderExisted(path: string): boolean{\n    try {\n      let stat = fileio.statSync(path)\n      return stat.isDirectory()\n    } catch (e) {\n      consoleInfo('SVGManager existFolder', e.message);\n      return false\n    }\n  }\n\n  \n  private _createFile(path: string): number {\n    return fileio.openSync(path, 0o100, 0o666);\n  }\n\n  \n  private _writeFile(path: string, content: ArrayBuffer | string): void{\n    try {\n      let fd = fileio.openSync(path, 0o102, 0o666);\n      fileio.ftruncateSync(fd);\n      fileio.writeSync(fd, content);\n      fileio.fsyncSync(fd);\n      fileio.closeSync(fd);\n    } catch (e) {\n      consoleInfo('SVGManager writeFile ', 'Failed to writeFile for ' + e);\n    }\n  }\n\n  \n  public getFilePath(onSuccess: (filesDir: string) => void): void {\n    let filesDir: string = GlobalContext.getContext().getObject(\"filesDir\") as string\n    onSuccess(filesDir);\n  }\n\n  \n  public parse(fileName: string, onSuccess: (result: string) => void, onFailed?: (error: Error) => void): void {\n    \n    let fileCompletePath = this._filePath + '/' + fileName;\n    let that = this;\n    fileio.readText(fileCompletePath)\n      .then( (str)=> {\n        try {\n          that._checker.check(str, ()=> {\n            let xmlInstance = new xml.ConvertXML();\n            let xmlResult = xmlInstance.convert(str, {\n              trim: false,\n              declarationKey: 'declaration',\n              instructionKey: 'instruction',\n              attributesKey: 'attributes',\n              textKey: 'text',\n              cdataKey: 'cdata',\n              doctypeKey: 'doctype',\n              commentKey: 'comment',\n              parentKey: 'parent',\n              typeKey: 'type',\n              nameKey: 'name',\n              elementsKey: 'elements'\n            });\n            if (xmlResult) {\n              if (typeof xmlResult === 'object') {\n                onSuccess(JSON.stringify(xmlResult));\n              } else {\n                onSuccess(xmlResult);\n              }\n            } else {\n              consoleInfo('SVGManager parse failed ', 'result is empty');\n              if (!!onFailed) {\n                onFailed(new Error('SVGManager parse failed, result is empty'));\n              }\n\n            }\n          })\n        } catch (e) {\n          consoleInfo('SVGManager parse checker failed ', e)\n          if (!!onFailed) {\n            onFailed(e);\n          }\n        }\n      }).catch((e: string|Error) => {\n      consoleInfo('SVGManager parse failed catch ', e as string);\n      if (!!onFailed) {\n        onFailed(e as Error);\n      }\n    })\n  }\n}"}
{"content": "/*\n * Copyright (c) 2023-2024 Huawei Device Co., Ltd.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport commonEventManager from '@ohos.commonEventManager';\nimport contextConstant from '@ohos.app.ability.contextConstant';\n// import ServiceExtensionAbility from '@ohos.app.ability.ServiceExtensionAbility';\nimport { AlarmServiceManager, AlarmCardUtil, NotificationUtil } from '@hmos/alarmclock';\nimport {\n  AlarmInfo,\n  CommonUtil,\n  EVENT_ID_KILL_ALARM_TIMEOUT_MESSAGE,\n  FullScreenType,\n  WantAgentUtil\n} from '@hmos/common';\nimport {\n  LogUtil,\n  ResourceManager,\n  AlarmStateManager,\n  TIME_TAG_ID_LIST_IN_ZH,\n  AlarmServiceType,\n  MILLIS_IN_SECOND,\n  SECOND_IN_MINUTE,\n  GlobalContext,\n} from '@hmos/common';\nimport Want from '@ohos.app.ability.Want';\nimport deviceInfo from '@ohos.deviceInfo'\nimport SnoozeManager from '@hmos/common/src/main/ets/manager/SnoozeManager';\nimport notificationManager from '@ohos.notificationManager';\nimport emitter from '@ohos.events.emitter';\n\nconst deviceType: string = deviceInfo.deviceType;\nconst TIME_SUBSCRIBE_INFO: CommonEventSubscribeInfo = {\n  events: [\n    commonEventManager.Support.COMMON_EVENT_SCREEN_OFF,\n  ],\n};\n\nconst TAG = 'AlarmService';\n\ntype CommonEventSubscriber = commonEventManager.CommonEventSubscriber;\ntype CommonEventSubscribeInfo = commonEventManager.CommonEventSubscribeInfo;\n\n/**\n * Service ability for alarm notification\n *\n * @since 2022-08-18\n */\nexport default class AlarmService {\n  private killAlarmMessageId: number = 0;\n  private commonEventSubscriber: CommonEventSubscriber = {} as CommonEventSubscriber;\n  private static instance: AlarmService;\n  private static isNotificationSubscribe = false;\n\n  public static getContext(): AlarmService {\n    if (!AlarmService.instance) {\n      AlarmService.instance = new AlarmService();\n    }\n    return AlarmService.instance;\n  }\n\n  private setInitInfo(want: Want): void {\n    // this.context.area = contextConstant.AreaMode.EL1;\n    // GlobalContext.getContext().setObject('clockContext', this.context);\n    GlobalContext.getContext().setObject('clockContextType', 'AlarmService');\n    // GlobalContext.getContext().setObject('AlarmServiceContext', this.context);\n    // GlobalContext.getContext().setObject('resourceManager', this.context.resourceManager);\n    GlobalContext.getContext().setObject('abilityWant', want);\n  }\n\n  async onCreate(want: Want): Promise<void> {\n    LogUtil.info(TAG, 'onCreate');\n    this.setInitInfo(want);\n    await AlarmServiceManager.createAudioWorker(true);\n    await ResourceManager.preloadStringResources(TIME_TAG_ID_LIST_IN_ZH);\n    this.subscribeCommentEvent();\n    if (!AlarmService.isNotificationSubscribe) {\n      await NotificationUtil.notificationSubscribeSystemSubscriber();\n      AlarmService.isNotificationSubscribe = true;\n    }\n    CommonUtil.requestCPUResources();\n    await CommonUtil.getWallpaper();\n  }\n\n  private serviceType(want?: Want) {\n    return want?.parameters?.serviceType\n  }\n\n  private isSnooze(want?: Want) {\n    return want?.parameters?.isSnooze\n  }\n\n  async onRequest(want: Want): Promise<void> {\n    LogUtil.info(TAG, `onRequest, want is: ${JSON.stringify(want)}`);\n    const serviceType = this.serviceType(want);\n    const alarmInfo: AlarmInfo = want?.parameters?.alarmInfo as AlarmInfo;\n    const isNotificationCloseButton = want?.parameters?.isNotificationCloseButton;\n    const isSnooze = this.isSnooze(want);\n    this.setInitInfo(want);\n    if (serviceType && serviceType === AlarmServiceType.ReStart) {\n      await AlarmServiceManager.dealAlarmWithServiceType(alarmInfo, serviceType as AlarmServiceType);\n      return;\n    }\n    if (serviceType && serviceType === AlarmServiceType.ToggleFullScreen) {\n      if (deviceType !== '2in1') {\n        await WantAgentUtil.triggerFullScreenAbility(FullScreenType.Alarm);\n      }\n      return;\n    }\n    if (serviceType && serviceType === AlarmServiceType.Update) {\n      await AlarmServiceManager.refreshNextAlertTime(want?.parameters?.forceRefreshTimer as boolean);\n      return;\n    }\n    if (!serviceType || !alarmInfo) {\n      LogUtil.info(TAG, 'Some parameter is not passed:' + serviceType + ',alarmInfo:' + JSON.stringify(alarmInfo));\n      return;\n    }\n    const isFiring = await AlarmStateManager.isFiring();\n    if (isFiring && serviceType === AlarmServiceType.Start) {\n      LogUtil.info(TAG, 'one alarm has Firing, so need to clearKillAlarmMessage:' + this.killAlarmMessageId);\n      this.clearKillAlarmMessage(this.killAlarmMessageId);\n    }\n    await AlarmServiceManager.dealAlarmWithServiceType(alarmInfo, serviceType as AlarmServiceType,\n      isNotificationCloseButton as boolean, isSnooze as boolean);\n    await this.dealKillAlarmMessage(alarmInfo, serviceType as AlarmServiceType);\n  }\n\n  async dealKillAlarmMessage(alarmInfo: AlarmInfo | undefined, serviceType: AlarmServiceType): Promise<void> {\n    const isFiring = await AlarmStateManager.isFiring();\n    const snoozeIds = await SnoozeManager.getSnoozedAlarmId();\n    const notificationCnt = await notificationManager.getActiveNotificationCount();\n    LogUtil.info(TAG, `dealKillAlarmMessage isFiring:${isFiring} cnt:${notificationCnt} snoozeIds:${JSON.stringify(snoozeIds)} `);\n    if (serviceType === AlarmServiceType.Start) {\n      if (isFiring && alarmInfo) {\n        LogUtil.info(TAG, 'alarm isFiring, so need to sendDelayedKillAlarmMessage');\n        this.killAlarmMessageId = this.sendDelayedKillAlarmMessage(alarmInfo);\n        AlarmCardUtil.notifyAlarmCardRingUpdate();\n      } else {\n        AlarmServiceManager.setScreenOff();\n        LogUtil.info(TAG, 'no alarm isFiring, so not need to sendDelayedKillAlarmMessage');\n      }\n    } else {\n      if (!isFiring) {\n        AlarmServiceManager.setScreenOff();\n        this.clearKillAlarmMessage(this.killAlarmMessageId);\n        await CommonUtil.terminateAlarmService();\n      }\n    }\n  }\n\n  onDestroy(): void {\n    LogUtil.info(TAG, 'onDestroy');\n    try {\n      GlobalContext.getContext().setObject('AlarmServiceContext', undefined);\n      this.clearKillAlarmMessage(this.killAlarmMessageId);\n      CommonUtil.terminateFullScreenAbility();\n      AlarmServiceManager.setScreenOff();\n      AlarmServiceManager.cancelSnoozeInput();\n      AlarmServiceManager.stopAudioWorker();\n      CommonUtil.releaseCPUResources();\n      this.unSubscribeCommentEvent();\n    } catch (error) {\n      LogUtil.error(TAG, 'onDestroy failed: ', JSON.stringify(error));\n    }\n\n  }\n\n  /**\n   * Send a delay message after the alarm starts to automatically snooze the alarm.\n   */\n  private sendDelayedKillAlarmMessage(alarmInfo: AlarmInfo): number {\n    this.clearKillAlarmMessage(this.killAlarmMessageId);\n    LogUtil.info(TAG, `send auto snooze message alarm id: ${JSON.stringify(alarmInfo.id)}, time:  ${alarmInfo.ringDuration! * SECOND_IN_MINUTE * MILLIS_IN_SECOND}`);\n    return setTimeout(async (alarmInfo: AlarmInfo) => {\n      LogUtil.info(TAG, 'start to execute delayAlarm for auto snooze');\n      const firingAlarmId = await AlarmStateManager.getAlarmId();\n      LogUtil.info(TAG, `auto snooze alarmInfo id: ${JSON.stringify(alarmInfo.id)}, firingAlarmId: ${firingAlarmId}`);\n      if (alarmInfo.id !== String(firingAlarmId)) {\n        return;\n      }\n      await AlarmServiceManager.delayAlarm(alarmInfo, true);\n      this.killAlarmMessageId = 0;\n      const isFiring = await AlarmStateManager.isFiring();\n      if (!isFiring) {\n        AlarmServiceManager.setScreenOff();\n        await CommonUtil.terminateAlarmService();\n      }\n    }, alarmInfo.ringDuration! * SECOND_IN_MINUTE * MILLIS_IN_SECOND, alarmInfo);\n  }\n\n  private clearKillAlarmMessage(messageId?: number): void {\n    try {\n      if (messageId) {\n        LogUtil.info(TAG, `clearKillAlarmMessage: ${messageId}`);\n        clearTimeout(messageId);\n        this.killAlarmMessageId = 0;\n      }\n    } catch (error) {\n      LogUtil.error(TAG, 'clearKillAlarmMessage failed: ', JSON.stringify(error));\n    }\n  }\n\n  private async subscribeCommentEvent(): Promise<void> {\n    this.commonEventSubscriber = await commonEventManager.createSubscriber(TIME_SUBSCRIBE_INFO);\n    if (!this.commonEventSubscriber) {\n      return;\n    }\n    commonEventManager.subscribe(this.commonEventSubscriber, async () => {\n      LogUtil.info(TAG, 'receive screen off event');\n      await AlarmServiceManager.delayFiringAlarm();\n    });\n\n    emitter.on({\n      eventId: EVENT_ID_KILL_ALARM_TIMEOUT_MESSAGE\n    }, async (eventData) => {\n      LogUtil.info(TAG, `receve EVENT_ID_KILL_ALARM_TIMEOUT_MESSAGE: ${JSON.stringify(eventData)}`);\n      await this.dealKillAlarmMessage(undefined, AlarmServiceType.Delay);\n    })\n  }\n\n  private unSubscribeCommentEvent(): void {\n    commonEventManager.unsubscribe(this.commonEventSubscriber, error => {\n      if (error) {\n        LogUtil.error(TAG, `Unsubscribe to common event failed because: ${JSON.stringify(error)}`);\n        return;\n      }\n      LogUtil.info(TAG, 'Unsubscribe to common event event successfully!');\n    });\n    emitter.off(EVENT_ID_KILL_ALARM_TIMEOUT_MESSAGE);\n  }\n};", "filepath": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs_nocomments/applications_clock/product/phone/src/main/ets/ServiceExtAbility/AlarmService.ets", "repo": "applications_clock", "isArkUI": false, "functions": [{"name": "sendDelayedKillAlarmMessage", "comment": "* Send a delay message after the alarm starts to automatically snooze the alarm.", "body": "private sendDelayedKillAlarmMessage(alarmInfo: AlarmInfo): number {\n    this.clearKillAlarmMessage(this.killAlarmMessageId);\n    LogUtil.info(TAG, `send auto snooze message alarm id: ${JSON.stringify(alarmInfo.id)}, time:  ${alarmInfo.ringDuration! * SECOND_IN_MINUTE * MILLIS_IN_SECOND}`);\n    return setTimeout(async (alarmInfo: AlarmInfo) => {\n      LogUtil.info(TAG, 'start to execute delayAlarm for auto snooze');\n      const firingAlarmId = await AlarmStateManager.getAlarmId();\n      LogUtil.info(TAG, `auto snooze alarmInfo id: ${JSON.stringify(alarmInfo.id)}, firingAlarmId: ${firingAlarmId}`);\n      if (alarmInfo.id !== String(firingAlarmId)) {\n        return;\n      }\n      await AlarmServiceManager.delayAlarm(alarmInfo, true);\n      this.killAlarmMessageId = 0;\n      const isFiring = await AlarmStateManager.isFiring();\n      if (!isFiring) {\n        AlarmServiceManager.setScreenOff();\n        await CommonUtil.terminateAlarmService();\n      }\n    }, alarmInfo.ringDuration! * SECOND_IN_MINUTE * MILLIS_IN_SECOND, alarmInfo);\n  }", "human_label": "", "scores": {"Code Reviewer_Coherence": 4, "Code Reviewer_Consistency": 4, "Code Reviewer_Fluency": 4, "Code Reviewer_Relevance": 4, "Original Code Author_Coherence": 4, "Original Code Author_Consistency": 4, "Original Code Author_Fluency": 3, "Original Code Author_Relevance": 3, "Code Editor_Coherence": 4, "Code Editor_Consistency": 4, "Code Editor_Fluency": 3, "Code Editor_Relevance": 4, "Systems Analyst_Coherence": 4, "Systems Analyst_Consistency": 4, "Systems Analyst_Fluency": 3, "Systems Analyst_Relevance": 4}, "criterion_averages": {"Coherence": 4, "Consistency": 4, "Fluency": 3.25, "Relevance": 3.75}, "comment_zh": "闹钟启动后发送延迟消息以自动暂停闹钟。"}], "token_count": 2163, "content_with_no_comment": "\n\nimport commonEventManager from '@ohos.commonEventManager';\nimport contextConstant from '@ohos.app.ability.contextConstant';\n\nimport { AlarmServiceManager, AlarmCardUtil, NotificationUtil } from '@hmos/alarmclock';\nimport {\n  AlarmInfo,\n  CommonUtil,\n  EVENT_ID_KILL_ALARM_TIMEOUT_MESSAGE,\n  FullScreenType,\n  WantAgentUtil\n} from '@hmos/common';\nimport {\n  LogUtil,\n  ResourceManager,\n  AlarmStateManager,\n  TIME_TAG_ID_LIST_IN_ZH,\n  AlarmServiceType,\n  MILLIS_IN_SECOND,\n  SECOND_IN_MINUTE,\n  GlobalContext,\n} from '@hmos/common';\nimport Want from '@ohos.app.ability.Want';\nimport deviceInfo from '@ohos.deviceInfo'\nimport SnoozeManager from '@hmos/common/src/main/ets/manager/SnoozeManager';\nimport notificationManager from '@ohos.notificationManager';\nimport emitter from '@ohos.events.emitter';\n\nconst deviceType: string = deviceInfo.deviceType;\nconst TIME_SUBSCRIBE_INFO: CommonEventSubscribeInfo = {\n  events: [\n    commonEventManager.Support.COMMON_EVENT_SCREEN_OFF,\n  ],\n};\n\nconst TAG = 'AlarmService';\n\ntype CommonEventSubscriber = commonEventManager.CommonEventSubscriber;\ntype CommonEventSubscribeInfo = commonEventManager.CommonEventSubscribeInfo;\n\n\nexport default class AlarmService {\n  private killAlarmMessageId: number = 0;\n  private commonEventSubscriber: CommonEventSubscriber = {} as CommonEventSubscriber;\n  private static instance: AlarmService;\n  private static isNotificationSubscribe = false;\n\n  public static getContext(): AlarmService {\n    if (!AlarmService.instance) {\n      AlarmService.instance = new AlarmService();\n    }\n    return AlarmService.instance;\n  }\n\n  private setInitInfo(want: Want): void {\n    \n    \n    GlobalContext.getContext().setObject('clockContextType', 'AlarmService');\n    \n    \n    GlobalContext.getContext().setObject('abilityWant', want);\n  }\n\n  async onCreate(want: Want): Promise<void> {\n    LogUtil.info(TAG, 'onCreate');\n    this.setInitInfo(want);\n    await AlarmServiceManager.createAudioWorker(true);\n    await ResourceManager.preloadStringResources(TIME_TAG_ID_LIST_IN_ZH);\n    this.subscribeCommentEvent();\n    if (!AlarmService.isNotificationSubscribe) {\n      await NotificationUtil.notificationSubscribeSystemSubscriber();\n      AlarmService.isNotificationSubscribe = true;\n    }\n    CommonUtil.requestCPUResources();\n    await CommonUtil.getWallpaper();\n  }\n\n  private serviceType(want?: Want) {\n    return want?.parameters?.serviceType\n  }\n\n  private isSnooze(want?: Want) {\n    return want?.parameters?.isSnooze\n  }\n\n  async onRequest(want: Want): Promise<void> {\n    LogUtil.info(TAG, `onRequest, want is: ${JSON.stringify(want)}`);\n    const serviceType = this.serviceType(want);\n    const alarmInfo: AlarmInfo = want?.parameters?.alarmInfo as AlarmInfo;\n    const isNotificationCloseButton = want?.parameters?.isNotificationCloseButton;\n    const isSnooze = this.isSnooze(want);\n    this.setInitInfo(want);\n    if (serviceType && serviceType === AlarmServiceType.ReStart) {\n      await AlarmServiceManager.dealAlarmWithServiceType(alarmInfo, serviceType as AlarmServiceType);\n      return;\n    }\n    if (serviceType && serviceType === AlarmServiceType.ToggleFullScreen) {\n      if (deviceType !== '2in1') {\n        await WantAgentUtil.triggerFullScreenAbility(FullScreenType.Alarm);\n      }\n      return;\n    }\n    if (serviceType && serviceType === AlarmServiceType.Update) {\n      await AlarmServiceManager.refreshNextAlertTime(want?.parameters?.forceRefreshTimer as boolean);\n      return;\n    }\n    if (!serviceType || !alarmInfo) {\n      LogUtil.info(TAG, 'Some parameter is not passed:' + serviceType + ',alarmInfo:' + JSON.stringify(alarmInfo));\n      return;\n    }\n    const isFiring = await AlarmStateManager.isFiring();\n    if (isFiring && serviceType === AlarmServiceType.Start) {\n      LogUtil.info(TAG, 'one alarm has Firing, so need to clearKillAlarmMessage:' + this.killAlarmMessageId);\n      this.clearKillAlarmMessage(this.killAlarmMessageId);\n    }\n    await AlarmServiceManager.dealAlarmWithServiceType(alarmInfo, serviceType as AlarmServiceType,\n      isNotificationCloseButton as boolean, isSnooze as boolean);\n    await this.dealKillAlarmMessage(alarmInfo, serviceType as AlarmServiceType);\n  }\n\n  async dealKillAlarmMessage(alarmInfo: AlarmInfo | undefined, serviceType: AlarmServiceType): Promise<void> {\n    const isFiring = await AlarmStateManager.isFiring();\n    const snoozeIds = await SnoozeManager.getSnoozedAlarmId();\n    const notificationCnt = await notificationManager.getActiveNotificationCount();\n    LogUtil.info(TAG, `dealKillAlarmMessage isFiring:${isFiring} cnt:${notificationCnt} snoozeIds:${JSON.stringify(snoozeIds)} `);\n    if (serviceType === AlarmServiceType.Start) {\n      if (isFiring && alarmInfo) {\n        LogUtil.info(TAG, 'alarm isFiring, so need to sendDelayedKillAlarmMessage');\n        this.killAlarmMessageId = this.sendDelayedKillAlarmMessage(alarmInfo);\n        AlarmCardUtil.notifyAlarmCardRingUpdate();\n      } else {\n        AlarmServiceManager.setScreenOff();\n        LogUtil.info(TAG, 'no alarm isFiring, so not need to sendDelayedKillAlarmMessage');\n      }\n    } else {\n      if (!isFiring) {\n        AlarmServiceManager.setScreenOff();\n        this.clearKillAlarmMessage(this.killAlarmMessageId);\n        await CommonUtil.terminateAlarmService();\n      }\n    }\n  }\n\n  onDestroy(): void {\n    LogUtil.info(TAG, 'onDestroy');\n    try {\n      GlobalContext.getContext().setObject('AlarmServiceContext', undefined);\n      this.clearKillAlarmMessage(this.killAlarmMessageId);\n      CommonUtil.terminateFullScreenAbility();\n      AlarmServiceManager.setScreenOff();\n      AlarmServiceManager.cancelSnoozeInput();\n      AlarmServiceManager.stopAudioWorker();\n      CommonUtil.releaseCPUResources();\n      this.unSubscribeCommentEvent();\n    } catch (error) {\n      LogUtil.error(TAG, 'onDestroy failed: ', JSON.stringify(error));\n    }\n\n  }\n\n  \n  private sendDelayedKillAlarmMessage(alarmInfo: AlarmInfo): number {\n    this.clearKillAlarmMessage(this.killAlarmMessageId);\n    LogUtil.info(TAG, `send auto snooze message alarm id: ${JSON.stringify(alarmInfo.id)}, time:  ${alarmInfo.ringDuration! * SECOND_IN_MINUTE * MILLIS_IN_SECOND}`);\n    return setTimeout(async (alarmInfo: AlarmInfo) => {\n      LogUtil.info(TAG, 'start to execute delayAlarm for auto snooze');\n      const firingAlarmId = await AlarmStateManager.getAlarmId();\n      LogUtil.info(TAG, `auto snooze alarmInfo id: ${JSON.stringify(alarmInfo.id)}, firingAlarmId: ${firingAlarmId}`);\n      if (alarmInfo.id !== String(firingAlarmId)) {\n        return;\n      }\n      await AlarmServiceManager.delayAlarm(alarmInfo, true);\n      this.killAlarmMessageId = 0;\n      const isFiring = await AlarmStateManager.isFiring();\n      if (!isFiring) {\n        AlarmServiceManager.setScreenOff();\n        await CommonUtil.terminateAlarmService();\n      }\n    }, alarmInfo.ringDuration! * SECOND_IN_MINUTE * MILLIS_IN_SECOND, alarmInfo);\n  }\n\n  private clearKillAlarmMessage(messageId?: number): void {\n    try {\n      if (messageId) {\n        LogUtil.info(TAG, `clearKillAlarmMessage: ${messageId}`);\n        clearTimeout(messageId);\n        this.killAlarmMessageId = 0;\n      }\n    } catch (error) {\n      LogUtil.error(TAG, 'clearKillAlarmMessage failed: ', JSON.stringify(error));\n    }\n  }\n\n  private async subscribeCommentEvent(): Promise<void> {\n    this.commonEventSubscriber = await commonEventManager.createSubscriber(TIME_SUBSCRIBE_INFO);\n    if (!this.commonEventSubscriber) {\n      return;\n    }\n    commonEventManager.subscribe(this.commonEventSubscriber, async () => {\n      LogUtil.info(TAG, 'receive screen off event');\n      await AlarmServiceManager.delayFiringAlarm();\n    });\n\n    emitter.on({\n      eventId: EVENT_ID_KILL_ALARM_TIMEOUT_MESSAGE\n    }, async (eventData) => {\n      LogUtil.info(TAG, `receve EVENT_ID_KILL_ALARM_TIMEOUT_MESSAGE: ${JSON.stringify(eventData)}`);\n      await this.dealKillAlarmMessage(undefined, AlarmServiceType.Delay);\n    })\n  }\n\n  private unSubscribeCommentEvent(): void {\n    commonEventManager.unsubscribe(this.commonEventSubscriber, error => {\n      if (error) {\n        LogUtil.error(TAG, `Unsubscribe to common event failed because: ${JSON.stringify(error)}`);\n        return;\n      }\n      LogUtil.info(TAG, 'Unsubscribe to common event event successfully!');\n    });\n    emitter.off(EVENT_ID_KILL_ALARM_TIMEOUT_MESSAGE);\n  }\n};"}