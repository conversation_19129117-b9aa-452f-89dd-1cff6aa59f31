#!/usr/bin/env python3
"""
端到端评测器
通过启动实际的检索服务来进行完整的评测流程
"""

import json
import time
import logging
import subprocess
import requests
import signal
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)
from utils.code_format import ClearCommentsForC

logger = logging.getLogger(__name__)


@dataclass
class TestCase:
    """测试用例"""

    query: str  # comment_zh
    ground_truth_file: str  # filepath (相对路径)
    ground_truth_content: str  # 新增：ground truth的内容
    repo: str
    original_filepath: str  # 原始完整路径


@dataclass
class QueryResult:
    """查询结果"""

    query: str
    ground_truth_file: str
    ground_truth_content: str  # 新增：ground truth的内容
    predicted_files: List[str]
    predicted_contents: List[str]  # 新增：预测结果的内容
    scores: List[float]
    execution_time_ms: float
    is_match: bool
    match_rank: Optional[int]


class ServiceManager:
    """服务管理器"""

    def __init__(self, venv_path: str = ".venv"):
        self.venv_path = venv_path
        self.current_process = None
        self.current_port = None

    def start_service(self, repo_path: str, repo_name: str, port: int = 5001) -> bool:
        """启动检索服务"""
        try:
            # 停止当前服务
            logger.info("准备启动新服务，先停止当前服务...")
            self.stop_service()

            # 检查端口是否可用，如果不可用则尝试清理
            if self._is_port_in_use(port):
                logger.info(f"端口 {port} 被占用，尝试清理...")
                self._force_clear_port(port)

                # # 再次检查端口
                # if self._is_port_in_use(port):
                #     logger.error(f"端口 {port} 仍被占用，无法启动服务")
                #     return False

            # 构建命令 - 直接使用python，不通过shell
            python_path = f"{self.venv_path}/bin/python"
            cmd = [
                python_path,
                "main.py",
                "--repo-path",
                repo_path,
                "--port",
                str(port),
                "--log-level",
                "WARNING",
                # "--clear-comments"
                "--no-scheduler",
            ]

            logger.info(f"启动新服务: {repo_name} -> {repo_path} -> 端口 {port}")
            logger.info(f"执行命令: {' '.join(cmd)}")

            # 直接启动python进程，不使用shell
            self.current_process = subprocess.Popen(
                cmd,
                stdout=sys.stdout,
                stderr=sys.stderr,
                preexec_fn=os.setsid,  # 创建新的进程组
            )
            self.current_port = port

            logger.info(f"新进程已启动，PID: {self.current_process.pid}")

            # 等待服务启动
            success = self._wait_for_service(port)

            if not success:
                # 如果启动失败，输出错误日志
                if self.current_process and self.current_process.poll() is not None:
                    stdout, stderr = self.current_process.communicate()
                    if stdout:
                        logger.error(
                            f"服务stdout: {stdout.decode('utf-8', errors='ignore')}"
                        )
                    if stderr:
                        logger.error(
                            f"服务stderr: {stderr.decode('utf-8', errors='ignore')}"
                        )

            return success

        except Exception as e:
            logger.error(f"启动服务失败: {e}")
            return False

    def _is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            result = subprocess.run(
                f"lsof -ti:{port}", shell=True, capture_output=True, timeout=5
            )
            return result.returncode == 0 and result.stdout.strip()
        except Exception:
            return False

    def _wait_for_service(self, port: int, timeout: int = 3600) -> bool:
        """等待服务启动并确保索引构建完成"""
        health_url = f"http://127.0.0.1:{port}/health"
        query_url = f"http://127.0.0.1:{port}/query"
        start_time = time.time()

        logger.info("等待服务启动和索引构建完成...")

        while time.time() - start_time < timeout:
            try:
                # 先尝试health端点
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    health_data = response.json()
                    # 检查索引状态
                    if health_data.get("status") == "healthy":
                        logger.info(f"服务已启动且索引构建完成: 端口 {port}")
                        # 额外等待2秒确保索引完全就绪
                        time.sleep(2)
                        return True
                    else:
                        logger.debug(
                            f"服务启动中，索引状态: {health_data.get('status', 'unknown')}"
                        )
            except requests.exceptions.RequestException:
                pass

            try:
                # 如果health端点不存在，尝试query端点
                response = requests.post(
                    query_url, json={"query": "test", "top_k": 1}, timeout=120
                )
                if response.status_code in [200, 400]:  # 400也表示服务在运行
                    logger.info(f"服务已启动: 端口 {port}")
                    # 额外等待2秒确保索引完全就绪
                    time.sleep(2)
                    return True
            except requests.exceptions.RequestException:
                pass

            time.sleep(3)  # 增加等待间隔

        logger.error(f"服务启动超时: 端口 {port}")
        return False

    def stop_service(self):
        """停止当前服务"""
        if self.current_process:
            try:
                logger.info(f"正在停止服务 (PID: {self.current_process.pid})...")

                # 先尝试温和终止
                self.current_process.terminate()
                try:
                    self.current_process.wait(timeout=10)
                    logger.info("服务已正常停止")
                except subprocess.TimeoutExpired:
                    logger.warning("服务未在10秒内停止，尝试强制终止...")
                    # 强制终止整个进程组
                    try:
                        os.killpg(os.getpgid(self.current_process.pid), signal.SIGKILL)
                        self.current_process.wait(timeout=5)
                        logger.info("服务已强制停止")
                    except Exception as kill_e:
                        logger.error(f"强制停止服务失败: {kill_e}")

            except Exception as e:
                logger.warning(f"停止服务时出现异常: {e}")

            self.current_process = None

        if self.current_port:
            # 等待端口释放，但不强制清理（避免误杀）
            logger.info(f"等待端口 {self.current_port} 释放...")
            time.sleep(3)
            self.current_port = None
            logger.info("端口已释放")

        if not self.current_process and not self.current_port:
            logger.info("没有运行中的服务需要停止")

    def _force_clear_port(self, port: int):
        """强制清理指定端口上的所有进程"""
        try:
            logger.info(f"强制清理端口 {port}...")
            current_pid = os.getpid()

            # 多次尝试清理端口
            for attempt in range(3):
                # 查找端口上的进程
                result = subprocess.run(
                    f"lsof -ti:{port}", shell=True, capture_output=True, timeout=5
                )
                if result.returncode == 0 and result.stdout.strip():
                    pids = result.stdout.decode().strip().split("\n")
                    # 过滤掉当前进程，避免自杀
                    pids_to_kill = [
                        pid
                        for pid in pids
                        if pid.strip() and int(pid.strip()) != current_pid
                    ]

                    if pids_to_kill:
                        logger.info(
                            f"第{attempt + 1}次尝试: 发现端口 {port} 上的进程: {pids_to_kill}"
                        )

                        # 逐个终止进程，避免误杀当前进程
                        for pid in pids_to_kill:
                            try:
                                # 先尝试温和终止
                                subprocess.run(
                                    f"kill -TERM {pid}",
                                    shell=True,
                                    capture_output=True,
                                    timeout=2,
                                )
                                time.sleep(1)

                                # 检查进程是否还存在
                                check_result = subprocess.run(
                                    f"kill -0 {pid}", shell=True, capture_output=True
                                )
                                if check_result.returncode == 0:
                                    # 进程还存在，强制终止
                                    subprocess.run(
                                        f"kill -9 {pid}",
                                        shell=True,
                                        capture_output=True,
                                        timeout=2,
                                    )
                                    logger.info(f"强制终止进程 {pid}")
                                else:
                                    logger.info(f"进程 {pid} 已正常终止")
                            except Exception as e:
                                logger.warning(f"终止进程 {pid} 时出现异常: {e}")

                        # 等待端口释放
                        time.sleep(2)
                    else:
                        logger.info(f"端口 {port} 上没有需要清理的外部进程")
                        break
                else:
                    logger.info(f"端口 {port} 已清空")
                    break

            # 最终等待
            time.sleep(1)
            logger.info(f"端口 {port} 清理完成")

        except Exception as e:
            logger.warning(f"清理端口 {port} 时出现异常: {e}")


# 导入我们改进的评估系统
from metrics_calculator import (
    MetricsCalculator as ImprovedMetricsCalculator,
)


class MetricsCalculator:
    """评估指标计算器 - 使用改进的对比评估功能"""

    def __init__(self):
        self.improved_calculator = ImprovedMetricsCalculator([1, 3, 5, 10])

    def calculate_metrics(self, results: List[QueryResult]) -> Dict:
        """计算评估指标 - 同时返回路径匹配和内容匹配的结果"""
        if not results:
            return self._empty_metrics()

        # 转换为改进评估器需要的格式
        evaluation_results_path = []
        evaluation_results_content = []
        query_times = []

        for result in results:
            query_times.append(result.execution_time_ms)

            # 路径匹配评估
            path_eval = self.improved_calculator.calculate_single_query_metrics(
                query=result.query,
                ground_truth_file=result.ground_truth_file,
                predicted_files=result.predicted_files,
                scores=result.scores,
            )
            evaluation_results_path.append(path_eval)

            # 内容匹配评估 - 现在使用真实的内容数据
            content_eval = self.improved_calculator.calculate_single_query_metrics(
                query=result.query,
                ground_truth_file=result.ground_truth_file,
                predicted_files=result.predicted_files,
                scores=result.scores,
                ground_truth_content=result.ground_truth_content,
                predicted_contents=result.predicted_contents,
            )
            evaluation_results_content.append(content_eval)

        # 聚合指标
        path_metrics = self.improved_calculator.aggregate_metrics(
            evaluation_results_path, query_times
        )
        content_metrics = self.improved_calculator.aggregate_metrics(
            evaluation_results_content, query_times
        )

        # 获取实际的K值
        avg_k_int = int(round(path_metrics.avg_k))

        # 转换为原有格式，但包含对比信息
        return {
            "path_matching": {
                "total_queries": path_metrics.total_queries,
                "exact_match_count": path_metrics.exact_match_count,
                "exact_match_rate": path_metrics.exact_match_rate,
                "avg_k": path_metrics.avg_k,
                "success_rate_at_1": path_metrics.success_rate_at_k.get(1, 0.0),
                "success_rate_at_3": path_metrics.success_rate_at_k.get(3, 0.0),
                "success_rate_at_5": path_metrics.success_rate_at_k.get(5, 0.0),
                "success_rate_at_k": path_metrics.success_rate_at_k.get(avg_k_int, 0.0),
                "precision_at_1": path_metrics.avg_precision_at_k.get(1, 0.0),
                "precision_at_3": path_metrics.avg_precision_at_k.get(3, 0.0),
                "precision_at_5": path_metrics.avg_precision_at_k.get(5, 0.0),
                "precision_at_k": path_metrics.avg_precision_at_k.get(avg_k_int, 0.0),
                "mrr_at_k": path_metrics.avg_mrr,
                "ndcg_at_k": path_metrics.avg_ndcg_at_k.get(avg_k_int, 0.0),
                "f1_score": path_metrics.avg_f1_at_k.get(
                    1, 0.0
                ),  # 使用F1@1作为F1 score
                "avg_query_time_ms": path_metrics.avg_query_time_ms,
                "max_query_time_ms": path_metrics.max_query_time_ms,
                "min_query_time_ms": path_metrics.min_query_time_ms,
            },
            "content_matching": {
                "total_queries": content_metrics.total_queries,
                "exact_match_count": content_metrics.exact_match_count,
                "exact_match_rate": content_metrics.exact_match_rate,
                "avg_k": content_metrics.avg_k,
                "success_rate_at_1": content_metrics.success_rate_at_k.get(1, 0.0),
                "success_rate_at_3": content_metrics.success_rate_at_k.get(3, 0.0),
                "success_rate_at_5": content_metrics.success_rate_at_k.get(5, 0.0),
                "success_rate_at_k": content_metrics.success_rate_at_k.get(
                    avg_k_int, 0.0
                ),
                "precision_at_1": content_metrics.avg_precision_at_k.get(1, 0.0),
                "precision_at_3": content_metrics.avg_precision_at_k.get(3, 0.0),
                "precision_at_5": content_metrics.avg_precision_at_k.get(5, 0.0),
                "precision_at_k": content_metrics.avg_precision_at_k.get(
                    avg_k_int, 0.0
                ),
                "mrr_at_k": content_metrics.avg_mrr,
                "ndcg_at_k": content_metrics.avg_ndcg_at_k.get(avg_k_int, 0.0),
                "f1_score": content_metrics.avg_f1_at_k.get(1, 0.0),
                "avg_query_time_ms": content_metrics.avg_query_time_ms,
                "max_query_time_ms": content_metrics.max_query_time_ms,
                "min_query_time_ms": content_metrics.min_query_time_ms,
            },
            # 保持向后兼容，使用路径匹配作为默认结果
            "total_queries": path_metrics.total_queries,
            "exact_match_count": path_metrics.exact_match_count,
            "exact_match_rate": path_metrics.exact_match_rate,
            "avg_k": path_metrics.avg_k,
            "success_rate_at_1": path_metrics.success_rate_at_k.get(1, 0.0),
            "success_rate_at_3": path_metrics.success_rate_at_k.get(3, 0.0),
            "success_rate_at_5": path_metrics.success_rate_at_k.get(5, 0.0),
            "success_rate_at_k": path_metrics.success_rate_at_k.get(avg_k_int, 0.0),
            "precision_at_1": path_metrics.avg_precision_at_k.get(1, 0.0),
            "precision_at_3": path_metrics.avg_precision_at_k.get(3, 0.0),
            "precision_at_5": path_metrics.avg_precision_at_k.get(5, 0.0),
            "precision_at_k": path_metrics.avg_precision_at_k.get(avg_k_int, 0.0),
            "mrr_at_k": path_metrics.avg_mrr,
            "ndcg_at_k": path_metrics.avg_ndcg_at_k.get(avg_k_int, 0.0),
            "f1_score": path_metrics.avg_f1_at_k.get(1, 0.0),
            "avg_query_time_ms": path_metrics.avg_query_time_ms,
            "max_query_time_ms": path_metrics.max_query_time_ms,
            "min_query_time_ms": path_metrics.min_query_time_ms,
        }

    def _empty_metrics(self) -> Dict:
        """空指标"""
        empty_single = {
            "total_queries": 0,
            "exact_match_count": 0,
            "exact_match_rate": 0.0,
            "avg_k": 0.0,
            "success_rate_at_1": 0.0,
            "success_rate_at_3": 0.0,
            "success_rate_at_5": 0.0,
            "success_rate_at_k": 0.0,
            "precision_at_1": 0.0,
            "precision_at_3": 0.0,
            "precision_at_5": 0.0,
            "precision_at_k": 0.0,
            "mrr_at_k": 0.0,
            "ndcg_at_k": 0.0,
            "f1_score": 0.0,
            "avg_query_time_ms": 0.0,
            "max_query_time_ms": 0.0,
            "min_query_time_ms": 0.0,
        }

        return {
            "path_matching": empty_single.copy(),
            "content_matching": empty_single.copy(),
            **empty_single,  # 向后兼容
        }


class EndToEndEvaluator:
    """端到端评测器"""

    def __init__(self, venv_path: str = ".venv"):
        self.service_manager = ServiceManager(venv_path)
        self.metrics_calculator = MetricsCalculator()
        self.repo_paths = {}  # 缓存解析出的仓库路径

    def load_test_data(self, jsonl_file: str) -> Dict[str, List[TestCase]]:
        """加载测试数据并按repo分组"""
        logger.info(f"加载测试数据: {jsonl_file}")

        repo_test_cases = defaultdict(list)

        with open(jsonl_file, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())

                    # 从filepath解析出仓库路径
                    original_filepath = data["filepath"]
                    repo_name = data["repo"]
                    repo_path = self._extract_repo_path(original_filepath, repo_name)
                    if not os.path.exists(repo_path):
                        raise ValueError(f"仓库路径不存在: {repo_path}")

                    # # TEMP TEST
                    # if repo_name != "ohos_mpchart": continue

                    # 缓存仓库路径
                    if repo_name not in self.repo_paths:
                        self.repo_paths[repo_name] = repo_path

                    # 解析每个函数的测试用例
                    for func in data.get("functions", []):
                        if "comment_zh" not in func or not func["comment_zh"].strip():
                            continue

                        # 提取相对路径
                        relative_path = self._extract_relative_path(
                            original_filepath, repo_name
                        )

                        test_case = TestCase(
                            query=func["comment_zh"].strip(),
                            ground_truth_file=relative_path,
                            ground_truth_content=ClearCommentsForC(
                                func.get("body", "")
                            ),  # 使用函数体作为ground truth，不是整个文件
                            repo=repo_name,
                            original_filepath=original_filepath,
                        )

                        repo_test_cases[repo_name].append(test_case)

                except Exception as e:
                    logger.warning(f"解析第{line_num}行失败: {e}")

        logger.info(f"加载完成: {len(repo_test_cases)} 个仓库")
        for repo, cases in repo_test_cases.items():
            logger.info(f"  - {repo}: {len(cases)} 个测试用例")
            logger.info(f"    仓库路径: {self.repo_paths.get(repo, 'Unknown')}")

        return dict(repo_test_cases)

    def _extract_repo_path(self, filepath: str, repo_name: str) -> str:
        """从完整文件路径中提取仓库路径"""
        # 找到repo名称在路径中的位置
        parts = filepath.split("/")
        try:
            repo_index = parts.index(repo_name)
            # 返回包含repo名称的完整路径
            repo_path_parts = parts[: repo_index + 1]
            return "/".join(repo_path_parts)
        except ValueError:
            logger.error(f"无法从路径 {filepath} 中找到仓库名称 {repo_name}")
            # 如果找不到，尝试从路径中推断
            # 假设仓库名称是路径中的某个目录
            for i, part in enumerate(parts):
                if repo_name in part:
                    return "/".join(parts[: i + 1])
            # 最后的fallback，返回文件所在目录
            return str(Path(filepath).parent)

    def _extract_relative_path(self, filepath: str, repo: str) -> str:
        """提取相对于仓库根目录的路径"""
        # 找到repo名称在路径中的位置
        parts = filepath.split("/")
        try:
            repo_index = parts.index(repo)
            # 返回repo之后的路径部分
            relative_parts = parts[repo_index + 1 :]
            return "/".join(relative_parts)
        except ValueError:
            # 如果找不到repo名称，返回文件名
            return Path(filepath).name

    def query_service(
        self, query: str, port: int = 5001, top_k: int = 10
    ) -> Tuple[List[str], List[str], List[float], float]:
        """查询检索服务"""
        url = f"http://127.0.0.1:{port}/query"

        payload = {"query": query, "top_k": top_k}

        start_time = time.time()
        try:
            response = requests.post(url, json=payload, timeout=120)
            execution_time = (time.time() - start_time) * 1000

            if response.status_code == 200:
                data = response.json()
                results = data.get("results", [])

                file_paths = []
                contents = []
                scores = []

                for result in results:
                    file_path = result.get("file_path", "")
                    content = result.get(
                        "text", ""
                    )  # 获取代码片段内容（字段名是text不是content）
                    score = result.get("score", 0.0)
                    file_paths.append(file_path)
                    contents.append(content)
                    scores.append(score)

                return file_paths, contents, scores, execution_time
            else:
                logger.error(
                    f"查询失败: {response.status_code} - {query} - {response.text}"
                )
                return [], [], [], execution_time

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"查询异常: {e} - {query}")
            return [], [], [], execution_time

    def _execute_single_query(
        self, test_case: TestCase, port: int, case_index: int, total_cases: int
    ) -> Optional[QueryResult]:
        """执行单个查询任务"""
        if not test_case.query.strip():
            return None

        logger.debug(f"查询 {case_index}/{total_cases}: {test_case.query[:50]}...")

        # 执行查询
        file_paths, contents, scores, exec_time = self.query_service(
            test_case.query, port=port
        )

        # 如果查询失败，跳过这个测试用例
        if not file_paths:
            logger.warning(f"查询 {case_index} 可能失败，跳过 - {test_case.query}")
            return None

        # 检查路径匹配
        is_path_match = test_case.ground_truth_file in file_paths
        path_match_rank = None
        if is_path_match:
            path_match_rank = file_paths.index(test_case.ground_truth_file) + 1

        return QueryResult(
            query=test_case.query,
            ground_truth_file=test_case.ground_truth_file,
            ground_truth_content=test_case.ground_truth_content,
            predicted_files=file_paths,
            predicted_contents=contents,
            scores=scores,
            execution_time_ms=exec_time,
            is_match=is_path_match,  # 这里先用路径匹配，后面会在MetricsCalculator中重新计算
            match_rank=path_match_rank,
        )

    def evaluate_repo(self, repo: str, test_cases: List[TestCase]) -> List[QueryResult]:
        """评测单个仓库"""
        logger.info(f"开始评测仓库: {repo} ({len(test_cases)} 个测试用例)")

        # 获取仓库路径
        repo_path = self.repo_paths.get(repo)
        if not repo_path:
            logger.error(f"未找到仓库 {repo} 的路径信息")
            return []

        # 检查仓库路径是否存在
        if not Path(repo_path).exists():
            logger.error(f"仓库路径不存在: {repo_path}")
            return []

        # 启动服务
        port = 5001
        if not self.service_manager.start_service(repo_path, repo, port):
            logger.error(f"无法启动服务: {repo}")
            return []

        results = []

        try:
            # 先进行健康检查确保服务正常
            try:
                health_response = requests.get(
                    f"http://127.0.0.1:{port}/health", timeout=5
                )
                if health_response.status_code != 200:
                    logger.error(f"服务健康检查失败: {health_response.status_code}")
                    return []
            except requests.exceptions.RequestException as e:
                logger.error(f"服务健康检查异常: {e}")
                return []

            # 执行并发查询
            logger.info(f"开始执行并发查询，并发数: 10, 共{len(test_cases)}条测试数据")

            with ThreadPoolExecutor(max_workers=10) as executor:
                # 提交所有查询任务
                future_to_index = {}
                for i, test_case in enumerate(test_cases):
                    future = executor.submit(
                        self._execute_single_query,
                        test_case,
                        port,
                        i + 1,
                        len(test_cases),
                    )
                    future_to_index[future] = i

                # 收集结果
                completed_count = 0
                for future in as_completed(future_to_index):
                    result = future.result()
                    if result is not None:
                        results.append(result)

                    completed_count += 1
                    if completed_count % 5 == 0:
                        matches = sum(1 for r in results if r.is_match)
                        logger.info(
                            f"  进度: {completed_count}/{len(test_cases)}, 匹配率: {matches / len(results) * 100:.1f}% (已完成查询数: {len(results)})"
                        )

        finally:
            # 停止服务
            self.service_manager.stop_service()

        matches = sum(1 for r in results if r.is_match)
        logger.info(f"仓库 {repo} 评测完成: {matches}/{len(results)} 匹配")

        return results

    def run_evaluation(self, jsonl_file: str) -> Dict:
        """运行完整评测"""
        logger.info("开始端到端评测")
        start_time = time.time()

        # 1. 加载测试数据并汇总
        logger.info("=== 步骤1: 加载和汇总测试数据 ===")
        repo_test_cases = self.load_test_data(jsonl_file)

        # 打印汇总信息
        total_test_cases = sum(len(cases) for cases in repo_test_cases.values())
        logger.info("数据汇总完成:")
        logger.info(f"  总仓库数: {len(repo_test_cases)}")
        logger.info(f"  总测试用例数: {total_test_cases}")
        for repo, cases in repo_test_cases.items():
            logger.info(f"  - {repo}: {len(cases)} 个测试用例")

        all_results = []
        repo_metrics = {}

        # 2. 逐个仓库评测
        logger.info("\n=== 步骤2: 逐个仓库评测 ===")
        for i, (repo, test_cases) in enumerate(repo_test_cases.items(), 1):
            logger.info(f"\n--- 评测仓库 {i}/{len(repo_test_cases)}: {repo} ---")
            try:
                results = self.evaluate_repo(repo, test_cases)
                all_results.extend(results)

                # 计算单个仓库的指标
                repo_metrics[repo] = self.metrics_calculator.calculate_metrics(results)

                # 输出仓库评测结果
                matches = sum(1 for r in results if r.is_match)
                if len(results) != 0:
                    logger.info(
                        f"仓库 {repo} 评测完成: {matches}/{len(results)} 匹配 ({matches / len(results) * 100:.1f}%)"
                    )

            except Exception as e:
                logger.error(f"评测仓库 {repo} 失败: {e}")
                import traceback

                logger.error(traceback.format_exc())
                continue

        # 计算总体指标
        overall_metrics = self.metrics_calculator.calculate_metrics(all_results)

        total_time = time.time() - start_time

        # 汇总结果
        summary = {
            "overall_metrics": overall_metrics,
            "repo_metrics": repo_metrics,
            "total_time_seconds": total_time,
            "total_repos": len(repo_test_cases),
            "successful_repos": len(repo_metrics),
        }

        self._print_summary(summary)

        return summary

    def _print_summary(self, summary: Dict):
        """打印评测摘要 - 包含路径匹配和内容匹配的对比"""
        logger.info("" + "=" * 80)
        logger.info("端到端评测结果摘要 - 路径匹配 vs 内容匹配对比")
        logger.info("=" * 80)

        overall = summary["overall_metrics"]
        path_metrics = overall.get("path_matching", overall)  # 向后兼容
        content_metrics = overall.get("content_matching", overall)

        logger.info(f"总查询数: {overall['total_queries']}")
        logger.info(f"平均输出K值: {overall.get('avg_k', 0.0):.2f}")

        # 对比表格
        logger.info(f"{'=' * 70}")
        logger.info("路径匹配 vs 内容匹配 对比结果")
        logger.info(f"{'=' * 70}")
        logger.info(f"{'指标':<15} {'路径匹配':<12} {'内容匹配':<12} {'差异':<10}")
        logger.info("-" * 70)

        # 成功率对比
        success_metrics_to_compare = [
            ("Success@1", "success_rate_at_1"),
            ("Success@3", "success_rate_at_3"),
            ("Success@5", "success_rate_at_5"),
            ("Success@k", "success_rate_at_k"),
        ]

        for display_name, key in success_metrics_to_compare:
            path_val = path_metrics.get(key, 0.0)
            content_val = content_metrics.get(key, 0.0)
            diff = content_val - path_val
            logger.info(
                f"{display_name:<15} {path_val:<12.3f} {content_val:<12.3f} {diff:>+7.3f}"
            )

        # 精度指标对比
        precision_metrics_to_compare = [
            ("P@1", "precision_at_1"),
            ("P@3", "precision_at_3"),
            ("P@5", "precision_at_5"),
            # ('P@k', 'precision_at_k'),
        ]

        for display_name, key in precision_metrics_to_compare:
            path_val = path_metrics.get(key, 0.0)
            content_val = content_metrics.get(key, 0.0)
            diff = content_val - path_val
            logger.info(
                f"{display_name:<15} {path_val:<12.3f} {content_val:<12.3f} {diff:>+7.3f}"
            )

        # 其他指标对比
        other_metrics_to_compare = [
            ("MRR@K", "mrr_at_k"),
            # ('nDCG@K', 'ndcg_at_k'),
        ]

        for display_name, key in other_metrics_to_compare:
            path_val = path_metrics.get(key, 0.0)
            content_val = content_metrics.get(key, 0.0)
            diff = content_val - path_val
            logger.info(
                f"{display_name:<15} {path_val:<12.3f} {content_val:<12.3f} {diff:>+7.3f}"
            )

        # 查询时延
        logger.info(f"{'=' * 40}")
        logger.info("查询时延统计:")
        logger.info(f"{'=' * 40}")
        logger.info(f"  平均: {overall['avg_query_time_ms']:.2f}ms")
        logger.info(f"  最大: {overall['max_query_time_ms']:.2f}ms")
        logger.info(f"  最小: {overall['min_query_time_ms']:.2f}ms")

        logger.info(f"总耗时: {summary['total_time_seconds']:.2f}秒")
        logger.info(
            f"成功评测仓库: {summary['successful_repos']}/{summary['total_repos']}"
        )

        # 分析结论
        logger.info(f"{'=' * 40}")
        logger.info("分析结论:")
        logger.info(f"{'=' * 40}")

        # 使用Success@k作为主要对比指标
        path_success_k = path_metrics.get("success_rate_at_k", 0.0)
        content_success_k = content_metrics.get("success_rate_at_k", 0.0)

        if content_success_k > path_success_k:
            improvement = (
                (content_success_k - path_success_k) / max(path_success_k, 0.001) * 100
            )
            logger.info("✅ 内容匹配表现更好")
            logger.info(f"   Success@k提升: {improvement:+.1f}%")
        elif content_success_k < path_success_k:
            decline = (
                (content_success_k - path_success_k) / max(path_success_k, 0.001) * 100
            )
            logger.info("⚠️  路径匹配表现更好")
            logger.info(f"   Success@k下降: {decline:+.1f}%")
        else:
            logger.info("➡️  两种匹配方式表现相同")

        # 各仓库详情对比
        logger.info(f"{'=' * 70}")
        logger.info("各仓库详情对比:")
        logger.info(f"{'=' * 70}")
        for repo, metrics in summary["repo_metrics"].items():
            repo_path = metrics.get("path_matching", metrics)
            repo_content = metrics.get("content_matching", metrics)

            logger.info(f"📁 {repo}:")
            logger.info(f"   平均输出K值: {repo_path.get('avg_k', 0.0):.2f}")
            logger.info(
                f"   路径匹配Success@k: {repo_path.get('success_rate_at_k', 0.0):.3f}"
            )
            logger.info(
                f"   内容匹配Success@k: {repo_content.get('success_rate_at_k', 0.0):.3f}"
            )

            # 简要指标对比
            path_s1 = repo_path.get("success_rate_at_1", 0.0)
            content_s1 = repo_content.get("success_rate_at_1", 0.0)
            path_s3 = repo_path.get("success_rate_at_3", 0.0)
            content_s3 = repo_content.get("success_rate_at_3", 0.0)
            path_s5 = repo_path.get("success_rate_at_5", 0.0)
            content_s5 = repo_content.get("success_rate_at_5", 0.0)
            path_sk = repo_path.get("success_rate_at_k", 0.0)
            content_sk = repo_content.get("success_rate_at_k", 0.0)

            path_p1 = repo_path.get("precision_at_1", 0.0)
            content_p1 = repo_content.get("precision_at_1", 0.0)
            path_p3 = repo_path.get("precision_at_3", 0.0)
            content_p3 = repo_content.get("precision_at_3", 0.0)
            path_p5 = repo_path.get("precision_at_5", 0.0)
            content_p5 = repo_content.get("precision_at_5", 0.0)
            path_pk = repo_path.get("precision_at_k", 0.0)
            content_pk = repo_content.get("precision_at_k", 0.0)

            path_mrr = repo_path.get("mrr_at_k", 0.0)
            content_mrr = repo_content.get("mrr_at_k", 0.0)

            path_ndcg = repo_path.get("ndcg_at_k", 0.0)
            content_ndcg = repo_content.get("ndcg_at_k", 0.0)

            logger.info(
                f"   Success@1: {path_s1:.3f} vs {content_s1:.3f} ({content_s1 - path_s1:+.3f})"
            )
            logger.info(
                f"   Success@3: {path_s3:.3f} vs {content_s3:.3f} ({content_s3 - path_s3:+.3f})"
            )
            logger.info(
                f"   Success@5: {path_s5:.3f} vs {content_s5:.3f} ({content_s5 - path_s5:+.3f})"
            )
            logger.info(
                f"   Success@k: {path_sk:.3f} vs {content_sk:.3f} ({content_sk - path_sk:+.3f})"
            )
            logger.info(
                f"   P@1: {path_p1:.3f} vs {content_p1:.3f} ({content_p1 - path_p1:+.3f})"
            )
            logger.info(
                f"   P@3: {path_p3:.3f} vs {content_p3:.3f} ({content_p3 - path_p3:+.3f})"
            )
            logger.info(
                f"   P@5: {path_p5:.3f} vs {content_p5:.3f} ({content_p5 - path_p5:+.3f})"
            )
            logger.info(
                f"   P@k: {path_pk:.3f} vs {content_pk:.3f} ({content_pk - path_pk:+.3f})"
            )
            logger.info(
                f"   MRR@K: {path_mrr:.3f} vs {content_mrr:.3f} ({content_mrr - path_mrr:+.3f})"
            )
            logger.info(
                f"   nDCG@K: {path_ndcg:.3f} vs {content_ndcg:.3f} ({content_ndcg - path_ndcg:+.3f})"
            )
            logger.info(
                f"   查询时延: 平均 {metrics.get('avg_query_time_ms', 0.0):.2f}ms"
            )


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="端到端评测工具")
    parser.add_argument(
        "--test-file", default="testset/testset_lite_3.jsonl", help="测试数据文件"
    )
    parser.add_argument("--venv-path", default=".venv", help="虚拟环境路径")
    parser.add_argument("--log-level", default="INFO", help="日志级别")

    args = parser.parse_args()

    # 设置日志
    logging.basicConfig(
        level=getattr(logging, args.log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # 屏蔽httpx的日志输出
    logging.getLogger("httpx").setLevel(logging.WARNING)

    # 运行评测
    evaluator = EndToEndEvaluator(args.venv_path)

    # 设置信号处理器，确保程序被中断时能正确清理
    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}，正在清理资源...")
        evaluator.service_manager.stop_service()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        summary = evaluator.run_evaluation(args.test_file)

        # 保存结果
        output_file = f"evaluation_summary_{int(time.time())}.json"
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        logger.info(f"\n详细结果已保存到: {output_file}")

    except KeyboardInterrupt:
        logger.warning("\n评测被用户中断")
        logger.info("正在清理资源...")
    except Exception as e:
        logger.error(f"评测失败: {e}")
        import traceback

        traceback.print_exc()
    finally:
        # 确保服务被停止
        evaluator.service_manager.stop_service()


if __name__ == "__main__":
    main()
